<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="id.co.bri.brimo">

    <queries>
        <package android:name="com.whatsapp" />
        <package android:name="com.whatsapp.w4b" />
    </queries>
    <queries>
        <intent>
            <action android:name="android.speech.RecognitionService" />
        </intent>
    </queries>
    <queries>
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE" />
        </intent>
    </queries>

    <uses-feature
        android:name="android.hardware.fingerprint"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera2.any"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />

    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.NFC" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.WRITE_IN" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Sensitive -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        tools:remove="android:maxSdkVersion" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.READ_INTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <application
        android:name=".BaseApp"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="false"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_brimo_adaptive"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:screenOrientation="portrait"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:replace="android:fullBackupContent"
        tools:targetApi="s">
        <activity
            android:name=".ui.activities.AnotherDeviceDetectedNewSkinActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.SplashScreenActivity"
            android:clearTaskOnLaunch="true"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name=".NewYearIcon"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/new_year_app_icon"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/new_year_app_icon_round"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".ChristmasIcon"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/christmas_app_icon"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/christmas_app_icon_round"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".BRIBirthdayIcon"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/bri_birthday_app_icon"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/bri_birthday_app_icon_round"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".IndependenceDayIcon"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/independence_day_app_icon"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/independence_day_app_icon_round"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".LunarIcon"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/lunar_app_icon"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/lunar_app_icon_round"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".RamadhanIcon"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ramadhan_app_icon"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ramadhan_app_icon_round"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".IdulFitriIcon"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/idul_fitri_app_icon"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/idul_fitri_app_icon_round"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>
        <activity-alias
            android:name=".DefaultIcon"
            android:enabled="true"
            android:exported="true"
            android:icon="@mipmap/ic_brimo_adaptive"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_brimo_adaptive"
            android:targetActivity=".ui.activities.SplashScreenActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name=".ui.activities.listrikrevamp.reskin.ScannerReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.transaction_process.TransactionProcessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.listrikrevamp.reskin.AddSavedListrikActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.pulsadata.reskin.AddSavedPulsaDataActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.pulsadata.reskin.FormPulsaDataReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pulsadata.reskin.InquiryPulsaDataReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pulsadata.reskin.ConfirmationPulsaDataActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />

        <activity

            android:name=".ui.activities.listrikrevamp.reskin.FormListrikReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.listrikrevamp.reskin.InquiryListrikReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.listrikrevamp.reskin.ConfirmOpenBillListrikReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.listrikrevamp.reskin.ConfirmClosedBillListrikReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.listrikrevamp.reskin.CetakTokenReskinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".ui.activities.InputPasswordNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.EditFastMenuNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull" />
        <activity
            android:name=".ui.activities.infopage.GeneralInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull" />
        <activity
            android:name=".ui.activities.activationdebit.ActivationDebitConfirmationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.activationdebit.ActivationDebitOtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.activationdebit.ActivationDebitActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.activationdebit.ActivationDebitCardSuccessActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.activationdebit.ActivationDebitOnboardingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.detailkartunewskin.HomeCardActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarNewSkin"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.rdnrevamp.dashboard.DashboardCekStatusRdnRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdnrevamp.dashboard.DashboardOnboardingRdnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdnrevamp.historyWithdraw.HistoryWithdrawRdnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdnrevamp.inquiryWithdraw.InquiryWithdrawlRdnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdnrevamp.detailRdn.DetailRdnRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdnrevamp.dashboard.DashboardRdnRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.RiplayActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />

        <!-- React Native Activity -->
        <activity
            android:name=".ui.activities.ReactNativeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/ReactNativeAppTheme"
            android:windowSoftInputMode="adjustResize"
            android:launchMode="standard" />

        <activity
            android:name=".ui.activities.alertmaintenance.AlertMaintenanceActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voiceassistant.VoiceAssistantRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.notification.NotificationSettingDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.notification.NotificationSettingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.birthdayonboarding.BirthdayOnboardingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/CustomTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.smarttransfer.ReceiptSmartTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.smarttransfer.TermAndConditionSmartTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.smarttransfer.AturRekeningSmartTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.smarttransfer.RekomendasiRekeningSmartTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.smarttransfer.KonfirmasiRekeningSmartTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.smarttransfer.DashboardSmartTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.FormEditSavedEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.TambahDaftarEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekeningreskin.ReceiptSuccessNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.FormBeliEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.ReceiptPendingRegistDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeBlueBarRevamp" />
        <activity
            android:name=".ui.activities.dplkrevamp.ReceiptRegistDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeBlueBarRevamp" />
        <activity
            android:name=".ui.activities.dplkrevamp.TermsAndConditionFtuDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.ConfirmFtuDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeBlueBarRevamp" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccSofListActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ccqrismpm.SofQrisActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.ChangePINDebitActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bripoin.SNKBripoinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.DetailProductFtuDplkActivity"
            android:exported="false"
            android:theme="@style/AppThemeBlueBarRevamp" />
        <activity
            android:name=".ui.activities.dplkrevamp.InquiryDplkRevampActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.general.ReceiptStatusActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.StepPersonalDataDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.StepPickProfileRiskDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.RiwayatSetoranBrifineDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.KonfirmasiInquiryGeneralActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.RiwayatKlaimDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.TambahDaftarDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.FormTopupDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.loaninappcc.LoanInAppRiwayatPengajuanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.loaninappcc.LoanInAppFormActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.loaninappcc.LoanInAppListSofCcActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.MerchantConnectActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.autograbfund.DashboardAutoGrabFundActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.donasirevamp.FormDonasiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.donasirevamp.InquiryDonasiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.donasirevamp.ListLembagaDonasiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.ekspedisi.WebviewEkspedisiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.MyLifestyleTransactionActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.ReceiptLifestyleActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.KonfirmasiLifestyleActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.shopping.WebviewShoppingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.shopping.ShoppingConfirmationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.WebviewLifestyleActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lifestyle.DashboardLifestyleActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeTransparentStatusBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.kesehatan.KonfirmasiLayananJanjiDokterActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferrevamp.DetailAftActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferrevamp.ListAftActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferrevamp.SuccessAftActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voiceassistant.VoiceAssistantActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull" />
        <activity
            android:name=".ui.activities.transferrevamp.KonfirmasiAftActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.property.InputBillingPropertyActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.property.FormPropertyActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.telkomrevamp.FormTelkomRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.telkomrevamp.TambahDaftarTelkomRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.nfcqrtap.NfcPaymentActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.depositorevamp.DashboardDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.depositorevamp.OnBoardingDepositoRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.depositorevamp.DetailDepositoRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transactionlimitinformation.TransactionLimitInformationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.ProductBriefLimitActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.SuccessLimitActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.DetailLimitCardActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.pengelolaankartu.ListCardTypesActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencanarevamp.KonfirmasiRencanaRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencanarevamp.InquiryPencairanRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencanarevamp.EditTargetMoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencanarevamp.DetailRencanaRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencanarevamp.FirstTimeRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencanarevamp.DashboardRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ubahdetailrencana.ChangeDetailPlanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.topuprencana.TopUpPlanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbnrevamp.DetailPortoSbnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.general.GeneralSNKWithPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.FormDataIuranDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.SimulasiBrifineDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.FirstTimeDplkActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.DetailBrifineDplkRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.DashboardDplkRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.SettingAutoPaymentActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.ListDplkOptionRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.FormBrifineKombinasiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.BrifineKombinasiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.FormPilihBrifineRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.InquiryAutoPaymentActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplkrevamp.OnboardingAutopaymentActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiGeneralInvestasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.StatusAmbilFisikActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.InquiryCetakEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.TentangAutoDebetActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbnrevamp.SbnSimulasiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbnrevamp.ListPortoSbnRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbnrevamp.ListBeliSbnRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.britamajuniorevamp.InquiryBritamaJunioRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.britamajuniorevamp.FormBritamaJunioRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.bukavalasrevamp.KonfirmasiBukaValasRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.bukavalasrevamp.FormBukaValasRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbnrevamp.DashboardSbnRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbnrevamp.FirstTimeSBNRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp" />
        <activity
            android:name=".ui.activities.bukarekening.bukavalasrevamp.InquiryBukaValasRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.britamarencanarevamp.InquiryBritamaRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.britamarencanarevamp.FormBritamaRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.halamancarirevamp.HalamanCariRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysVisible" />
        <activity
            android:name=".ui.activities.topuprevamp.TopUpRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampAppCompat"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dashboardrevamp.MenuListGeneralActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.signal.FormSignalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransirevamp.DetailBeliAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransirevamp.FormAsuransiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransirevamp.PanduanClaimAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransirevamp.InformasiProductAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransirevamp.DetailAsuransiRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransirevamp.ListAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransirevamp.DashboardAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.DetailVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.ProcessPageVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.DetailProductVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.SuccessPageVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/CustomTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.ConfirmationPageVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.SelectProductVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.InputLabelVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.virtualdebitcard.OnBoardingVDCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.EditFastMenuRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.MicrositeCallbackActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.InquiryRdnRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.MutasiRdnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.TopupRdnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.portofolioksei.SyaratRegisKseiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.portofolioksei.RegistrasiKseiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.portofolioksei.DetailKseiWithTabActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.portofolioksei.DashboardKseiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dashboardInvestasi.StoryInvestasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dashboardInvestasi.DashboardInvestasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.safetymode.KonfirmasiSafetyModeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voucher.SyaratKetentuanVoucherActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voucher.HistoryVoucherActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voucher.SnkVoucherActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voucher.CaraRedeemVocActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voucher.InquiryKonfirmasiVoucherActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voucher.DetailVoucherActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voucher.VoucherActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.listrikrevamp.InquiryListrikRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.listrikrevamp.TambahDaftarListrikRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.listrikrevamp.CetakTokenRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.listrikrevamp.FormListrikRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.receipt.reskin.ReceiptReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pendidikanrevamp.TambahPendidikanRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pendidikanrevamp.FormPendidikanRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.form.FormGeneralRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.form.TambahGeneralRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.WebViewTravelActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.general.GeneralSyaratKetentuanNoButton"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.RiwayatTransaksiEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.GrafikEmasInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.GrafikJualEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.GrafikBeliEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.FormJualEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.EditSetoranEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.ReceiptGagalAFTActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.DetailRekeningEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.ReceiptGagalRegistrasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.general.GeneralSNKwithChecklistActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.PilihKantorPegadaianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.InquiryBeliEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.ProductBriefEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.DashboardEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.InquiryRegisEmasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.InformasiPribadiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.emas.OnboardEmsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.GeneralSyaratRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qr.crossborder.KonfirmasiQrCrossborderActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qr.crossborder.InquiryQrCrossborderActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".ui.activities.RequestDownloadMutationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.PreviewMemorandumActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.MutationFilterActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize"/>
        <activity
            android:name=".ui.activities.CashbackAllActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.DetailPromoCashbackActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.parking.SuccessParkingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.parking.InquiryScanParkingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ListUpdateRekeningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ListRekeningCategoryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.receipt.ReceiptRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiGeneralRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.TabunganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamajunio.PilihKantorJunioActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamajunio.DetailRekeningJunioActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamajunio.ReceiptOpenJunioActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.PendingDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.earlyredemption.RiwayatPencairanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.earlyredemption.ReceiptEarlyRedeemActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.earlyredemption.ConfirmationErlyRedeemActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.earlyredemption.InquiryEarlyRedeemActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.earlyredemption.DetailSbnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ListProductRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.EsbnRdnFormActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.EsbnRdnKonfirmasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.ReceiptNonTransactionActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.EsbnInquiryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.EsbnDataPekerjaanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.EsbnDataDiriActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.EsbnProductBriefRegisActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.regisSbn.EsbnOnboardingRegisActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.InquiryOpenDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.TambahDaftarDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ReceiptPendingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".ui.activities.deposito.InfoBukaDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.DepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.InquiryPenutupanDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.PenaltiPenutupanDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.UbahPerpanjanganDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransi.MicrositeLamaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustResize">
            <meta-data
                android:name="android.webkit.WebView.EnableSafeBrowsing"
                android:value="true" />
        </activity>
        <activity
            android:name=".ui.activities.FormMpnActivity"
            android:exported="false"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.launcher.BrowserIntentActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Transparent"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.PFMCategoryDetailActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.travel.DetailBusPulangActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.DetailTiketTravelActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.ReceiptTravelActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.FilterHistoryTravelActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.HistoryTravelActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.KonfirmasiTravelAvtivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.InquiryBusPulangActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.BusInquiryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.KonfirmasiKursiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.PickSeatPulangActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.PickSeatActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.InputPenumpangActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.DetailPemesanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.FormPemesananTiketActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.BantuanTravelActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.DetailBusActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.InquiryBusActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.FormBusActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.TravelMenuActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.LaporanTransaksiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.InformasiTransaksiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.DetailComplainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ListComplainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.PengaduanTrxGagalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.PengaduanTransaksiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.SelfServiceActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.OneShildActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormBayarPinjamanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.BrigunaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.DetailPinjamanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InfoPinjamanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasi.nds.RegisInfoNdsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasi.nds.RegisPasswordNdsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasi.nds.RegisUsernameNdsActivity"
            android:autoVerify="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data
                    android:host="brimo.bri.co.id"
                    android:path="/nds/reg"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.registrasi.nds.RegisOtpNdsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.RdnOtpActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.rdn.CekStatusRdnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step11RdnMoreInformationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step12RdnSignatureActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step10RdnRiskProfile"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step9FormRdnJobActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step8FormRdnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step7RdnSelfieResultActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step6RdnTakeSelfieActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step5RdnSelfieInformationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step4RdnResultKtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step3RdnTakeKtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step2RdnKtpInfromationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.Step1RdnDocumentActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.DescriptionRdnProductActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.OnBoardingRdn2Activity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.OnBoardingRdn1Activity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rdn.DashboardRDNActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.BrigunaDigitalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.CetakTokenActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.GeneralSyaratActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.ReceiptTransferInternationalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".ui.activities.asuransi.ListProdukAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransi.InquiryMicrositeBrivaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.asuransi.MicrositeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarWebView"
            android:windowSoftInputMode="adjustResize">
            <meta-data
                android:name="android.webkit.WebView.EnableSafeBrowsing"
                android:value="true" />
        </activity>
        <activity
            android:name=".ui.activities.asuransi.JenisTransaksiAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.openaccount.KonfirmasiGeneralOpenAccountActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.openaccount.PendingGeneralOpenAccountActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.openaccount.InquiryGeneralOpenAccountActivity"
            android:exported="false"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.PilihKantorGeneralActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.AliasBiFastActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ProductWebviewActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.simpedes.InquiryBrifineActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.ReceiptAmkkmActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.KonfirmasiAsuransiAmkkmActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.InquiryAsuransiAmkkmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.FormAsuransiAmkkmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.DetailAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.ProductListAmkkmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.FormTopupBrifineActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.KameraBrifineActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.simpedes.DetailBrifineActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.KonfirmasiBrifineActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.simpedes.FormBrifineActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.InquirySimpedesOpenActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.KonfirmasiSimpedesActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.InfoImpianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.FormTopupImpianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.FormPencairanImpianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.EditNamaImpianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.DetailImpianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.InfoSimpedesActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.simpedes.FormImpianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.PilihKantorValas"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.FormAktivasiCcSofActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.GeneralOtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.InfoRekeningCcSofActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.RekeningCcSofActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiKonversiValasActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryKonversiVallasActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormKonversiVallasActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.PendingBukaValasActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.KonfirmasiBukaValasActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.InquiryBukaValasActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.FormBukaValasActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.RincianProdukValas"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.PilihJenisTabunganValas"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukaValas.RecieptBukaValasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.EditTfInternationalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.KonfirmasiTransferInternasionalActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.SavedTfInternationalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.FormTfInternasionalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.TransferStepSatuActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.TransferStepDuaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.TransferStepTigaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.SaveTfInternasionalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.InquiryTfInternasionalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.UbahPinBaru2Activity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.UbahPinValidasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.UbahPinBaruActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.UbahPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.UbahPasswordOtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.UbahPasswordBaruActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.UbahPasswordActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarPdamActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormPdamActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InfoFastMenuActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.EditUsernameActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiQrMPMActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryQrMPMActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".ui.activities.QrMPMCodeTambahActivity" />
        <activity
            android:name=".ui.activities.QrMPMActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.EditFastMenuActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarBpjsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormBpjsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormLTMPTActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qr.InquiryQrTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qr.KonfirmasiQrActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qr.PendingTransferQrActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.QrTransferCodeTambahActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.QrTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.NotificationListActivity"
            android:exported="false"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinConfirmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.CatatanKeuanganEditActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.CatatanKeuanganDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinCreateActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinInputDataActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinVerifEmailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ReceiptInboxActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.PencairanRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.DetailRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.RekeningRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.KonfirmasiDataRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.SyaratRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.KonfirmasiRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.HitungRencanaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.PilihRekeningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamarencana.PilihJenisTabunganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qrdagang.HistoryMerchantActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.RekeningAkunActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InfoRekeningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.RecieptBrizziActivty"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TapBrizziAktivasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TapBrizziAktivasiNonUserActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiBrizziOnlineActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplk.InfoDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.BlokirKartuActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.qrdagang.QrScannerActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qrdagang.QrInputActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.qrdagang.QrMerchantActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.DaftarCcActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.CeriaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarWebView"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.FormDonasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.DetailPromoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.AllPromoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LihatLebihPromoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.PilihAtmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.PilihanKantorTerdekatActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InfoSahamActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InfoKursActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bripoin.DetailAkunActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinFastActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinKonfirmasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinBuatBaruActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinOtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahTransferAliasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LupaPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.EditAlliasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryQrActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormQrActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.base.BaseFormNosavedActivity"
            android:exported="false"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormKaiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarTelevisiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.FormAsuransiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormTelevisiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarCicilanActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormCicilanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryBrizziActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.CekBrizziSatuActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.CekBrizziDuaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.CekBrizziDariLuarActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:permission=""
            android:theme="@style/AppThemeBlueBar">
            <intent-filter>
                <action android:name="android.nfc.action.TECH_DISCOVERED" />
            </intent-filter>

            <meta-data
                android:name="android.nfc.action.TECH_DISCOVERED"
                android:resource="@xml/nfc_tech_filter" />
        </activity>
        <activity
            android:name=".ui.activities.InfoSaldoBrizzi"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InfoSaldoBrizziNonUserActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormBrizziActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.deposito.DetailDepositoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiTarikActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiTartunNdsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.tartun.ReceiptTarikActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.tartun.PetunjukActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.tartun.FormTarikTunaiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.DetailPusatBantuanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.SubPusatBantuanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.PusatBantuanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KontakKamiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarPascaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.SyaratKetentuanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormPascaBayarActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.PilihTanggalMutasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.MutasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ScannerActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.TambahDaftarKreditActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarBrivaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarWalletActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.TambahDaftarPlnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryGeneralCloseActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiGeneralActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryGeneralOpenActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryPlnTokenActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormKreditActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormBrivaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormWalletActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormPlnTokenActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.DetailRekeningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.RekeningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.waiting.WaitingActivity"
            android:autoVerify="true"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data android:scheme="https" />
                <data android:scheme="http" />
                <data
                    android:host="brimo.bri.co.id"
                    android:path="/app/login" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.KonfirmasiPinActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.BuatPinActivity"
            android:exported="false" />
        <activity
            android:name=".ui.activities.FormPulsaPaketActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".ui.activities.SetCalendarActivity"
            android:exported="false"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.TambahCatatanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.TambahCatatanActivity2"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.DashboardIBActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeTransparentStatusBar" />
        <activity
            android:name=".ui.activities.InboxFilterActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.AskActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.LoginActivity"
            android:autoVerify="true"
            android:exported="false"
            android:label="@string/title_activity_login"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.ReceiptActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".ui.activities.inforekeningnewskin.ListCardTypesNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarNewSkin"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FastMenuActivity"
            android:autoVerify="true"
            android:exported="true"
            android:label="@string/title_activity_login"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data
                    android:host="brimo.bri.co.id"
                    android:path="/app/feature"
                    android:scheme="https" />
                <data
                    android:host="brimo.bri.co.id"
                    android:path="/app/menu"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.FastMenuNewSkinActivity"
            android:autoVerify="true"
            android:exported="true"
            android:label="@string/title_activity_login"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data
                    android:host="brimo.bri.co.id"
                    android:path="/app/feature"
                    android:scheme="https" />
                <data
                    android:host="brimo.bri.co.id"
                    android:path="/app/menu"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.CatatanKeuanganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.PickCategoryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity android:name=".ui.activities.ComingSoonActivity" /> <!-- <activity -->
        <!-- android:name=".ui.activities.SplashScreenActivity" -->
        <!-- android:exported="true" -->
        <!-- android:permission="" -->
        <!-- android:screenOrientation="portrait" -->
        <!-- android:theme="@style/SplashTheme"> -->
        <!-- <intent-filter> -->
        <!-- <action android:name="android.intent.action.MAIN" /> -->
        <!-- <category android:name="android.intent.category.LAUNCHER" /> -->
        <!-- </intent-filter> -->
        <!-- </activity> -->
        <activity
            android:name=".ui.activities.VerifikasiPinActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.FormEditSavedActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormEditSavedReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.PendingTransferBankLainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" /> <!-- START TRANSFER OLD -->
        <activity
            android:name=".ui.activities.TambahDaftarTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".ui.activities.InquiryTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiTransferActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.NotificationRouterActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.activities.KeamananActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bripoin.InfoBriPointActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rtgs.DetailTransferRtgsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.rtgs.InquiryTransferRtgsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplk.OnboardingDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplk.DescriptionDplkProductActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplk.FormOpenDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplk.PilihJenisBrifineActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dplk.BrifineKombinasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" /> <!-- ESBN ACTIVITY -->
        <activity
            android:name=".ui.activities.dplk.InquiryOpenDplkActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" /> <!-- ESBN ACTIVITY -->
        <activity
            android:name=".ui.activities.sbn.TutorialPembayaranSbnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.DetailPembelianSbnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.DetailBeliSbnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.RiwayatPembelianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.TentangSbnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.BeliSBNActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.DashboardESBNActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.ConfirmationESBNActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.TermAndConditionESBNActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bripoin.BripoinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarWebView"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.sbn.InquiryMpnSbnActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.KonfirmasiTravelTrainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.TrainInquiryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.ReceiptTravelTrainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.EditEmailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.Otp4DigitDefaultActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".ui.activities.lupausername.WaitingLupaUsernameActivity"
            android:autoVerify="true"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data
                    android:host="brimo.bri.co.id"
                    android:path="/for/user"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.lupausername.FormLupaUsernameActivity"
            android:exported="false"
            android:permission=""
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InfoForgotUserPassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.GeneralOtpDigitHpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.GeneralPasswordActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lupapassword.WaitingLupaPasswordActivity"
            android:autoVerify="true"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data
                    android:host="brimo.bri.co.id"
                    android:path="/for/pass"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.lupapassword.KonfirmasiLupaUserPassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lupapassword.FormLupaPasswordActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.saldodompetdigital.SmartTopUpEwalletActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.saldodompetdigital.HubungkanDompetDigitalActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.saldodompetdigital.WebViewBindingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cardless.PetunjukSetorActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cardless.InformasiLimitSetorTunaiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cardless.SetorTunaiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cardless.FormSetorTunaiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ibbiz.OnboardingIbbizActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ibbiz.RekeningIbbizActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ibbiz.DataPerusahaanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ibbiz.KonfirmasiIbbizActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.GeneralWebviewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.britamajunio.FormBritamaJunioActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamajunio.InquiryOpenJunioActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamajunio.KonfirmasiOpenJunioActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.britamajunio.AboutAutoDebetActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengkiniandata.PengkinianDataActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengkiniandata.FormInformasiDiriPengkinianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengkiniandata.FormInformasiPekerjaanPengkinianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengkiniandata.KonfirmasiPengkinianActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferinternasional.TransferInternasionalOnBoardingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".ui.activities.finansialrek.WaitingFinansialActivity"
            android:autoVerify="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data
                    android:host="brimo.bri.co.id"
                    android:path="/brimo/fin"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.finansialrek.PerekamanVideoFinansialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.finansialrek.InputDataFinansialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.finansialrek.KameraKtpFinansialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.finansialrek.TutorialFotoFinansialActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.finansialrek.ReceiptNonTransactionFinrekActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pbb.TambahDaftarPbbActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pbb.FormPbbActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ChatBankingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.FormChatBankingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.ProductBriefBukaRekeningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiDokumenActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiCameraActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlackBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiInputDataActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiBCFUActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiPanduanGantiNomorActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiOtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiCheckPointActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiVerifyEmailActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiVerifyWajahActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiPendingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiInformationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiUserPassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiConfirmPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiOtpPrivyActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.receipt.ReceiptAbnormalRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.PendingTabunganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.ReceiptTabunganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.PilihanKantorGeneralActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.KonfirmasiTabunganRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.PilihanKantorActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekening.InquiryProductTabunganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferrevamp.FormEditSavedRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.brivarevamp.FormBrivaRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.brivarevamp.TambahDaftarBrivaRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.brivarevamp.InquiryBrivaOpenRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.InquiryKonfirmasiBrivaRevampCloseActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevampFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.brivarevamp.InquiryBrivaPartialRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voip.VoipActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.KonfirmasiGeneralOpenRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pulsadata.InquiryPulsaRevampRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pulsadata.FormPulsaDataRevActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferrevamp.InquiryTransferRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferrevamp.TambahTransferAliasRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.transferrevamp.FormTransferAliasRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingRekeningActivity"
            android:autoVerify="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:permission=""
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.LAUNCHER" />

                <data
                    android:host="brimo.bri.co.id"
                    android:path="/onboarding/referral"
                    android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingTabunganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingProductTabunganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingPilihKantorActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingCameraActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlackBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingInputDataActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingVerifyEmailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingOtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingVerifyWajahActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingPendingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingDataPribadiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingCheckPointActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingDataAlamatActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingDataPekerjaanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingDataKeuanganActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingInformationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingKonfirmasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingSyaratKetentuanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingUserPassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingConfirmPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingOtpPrivyActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.onboardingrevamp.OnboardingReceiptActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.travel.WebviewKonfirmasiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.LoadingMNVActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.InfoUbahPinAtmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.UbahPinAtmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.UbahPinAtmNewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.KonfirmUbahPinAtmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.InputNomorKtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.VerifikasiOtpReissueActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.TambahKartuCcActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.SyaratKetentuanCcActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.VerifikasiOtpBindingCcActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintBrizziActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintCekBrizziActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.ssc.ComplaintNonTransaksiActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:theme="@style/AppThemeBlueBar" />
        <activity
            android:name=".ui.activities.ssc.ComplaintEdcBriActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintPajakRetribusiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintPencairanBrigunaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintRtgsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintCicilBrigunaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintTfInternasionalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.ssc.ComplaintKartuKreditActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dompetdigitalrevamp.FormDompetDigitalRevamp"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dompetdigitalrevamp.PilihWalletActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dompetdigitalrevamp.InputNomorActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dompetdigitalrevamp.InquiryDompetDigitalRevampActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.FormDompetDigitalReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.InquiryDompetDigitalReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.ConfirmationDompetDigitalReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.SearchSavedHistoryDompetDigitalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.TncDompetDigitalReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.HubungkanDompetDigitalReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.SuccessBindingDompetDigitalReskinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.dompetdigitalreskin.EditBindingDompetDigitalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.ibbiz.IbbizActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.EstatementCcActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pajakhoreka.FormPajakHorekaActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lupausername.VerifikasiOtpNoHpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.lupausername.VerifikasiOtpEmailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.waiting.OtpSmsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.PengelolaanKartuNewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.pengelolaankartu.DetailKelolaKartuNewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccProductListActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.cc_sof.BindingCcKkiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccConfirmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccOtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccProductDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccInformationPersonalActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.receipt.ReceiptRevampBindingKkiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccInformationAddressActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccInformationJobActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccPhotoSelfieActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.applyvcc.ApplyVccPhotoKtpActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.registrasirevamp.RegistrasiMNVActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.voip.CategoryVoipActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.infobrimo.InfoBrimoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillAddMemberActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillOnboardingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeTransparentStatusBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillHistoryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillEditFormActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillShareActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillConfirmationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.splitbill.SplitBillDetailPerMemberActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.onboardingnewskin.VerifikasiWajahActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull" />
        <activity
            android:name=".ui.activities.newskinonboarding.OnboardingVerifyEKYCActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.onboardingnewskin.BuatKataKunciActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.newskinonboarding.pass.EnterPassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.newskinonboarding.pin.EnterCurrentPinPassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.newskinonboarding.pin.EnterCurrentPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.newskinonboarding.VerificationProcessPrivyActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.newskinonboarding.VerificationSuccessPrivyActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.newskinonboarding.pin.EnterCreatePinConfirmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.newskinonboarding.pin.EnterCreatePinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.changepassnewskin.ChangePassActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.changepassnewskin.ChangePassAccountActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarNewSkin"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.changepinnewskin.ChangePinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeWhiteBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.changepinnewskin.ChangeNewPinConfirmActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeWhiteBar"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.activities.changepinnewskin.ChangeNewPinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".ui.activities.SuccessNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustResize"/>

        <activity
            android:name=".ui.activities.newskinonboarding.OnboardingOtpNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarRevamp"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.bukarekeningreskin.TabunganNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".ui.activities.bukarekeningreskin.ProductBriefBukaRekeningNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBar"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.bukarekeningreskin.InquiryProductTabunganNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarNewSkin"
            android:windowSoftInputMode="adjustResize"/>

        <activity
            android:name=".ui.activities.bukarekeningreskin.KonfirmasiTabunganRevNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarNewSkin"
            android:windowSoftInputMode="adjustResize"/>

        <activity
            android:name=".ui.activities.bukarekeningreskin.ReceiptTabunganNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarNewSkin"
            android:windowSoftInputMode="adjustResize"/>

        <activity
            android:name=".ui.activities.carddetailnewskin.VirtualCardDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.carddetailnewskin.VirtualDebitCardInfoActivity"
            android:exported="false"
            android:theme="@style/AppThemeNewSkinFull" />

        <activity
            android:name=".ui.activities.carddetailnewskin.VirtualDebitCardTypeActivity"
            android:exported="false"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.carddetailnewskin.VirtualDebitCardLabelActivity"
            android:exported="false"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.carddetailnewskin.VirtualDebitCardConfirmationActivity"
            android:exported="false"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name=".ui.activities.carddetailnewskin.VirtualDebitCardTypeDetailActivity"
            android:exported="false"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="adjustPan"/>

        <activity
            android:name=".ui.activities.carddetailnewskin.PyhsicCardDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"/>

        <activity
            android:name=".ui.activities.inforekeningnewskin.InformasiRekeningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <activity
            android:name=".ui.activities.inforekeningnewskin.EditAliasNameActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <activity
            android:name=".ui.activities.inforekeningnewskin.ProxyBIFastActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <activity
            android:name=".ui.activities.inforekeningnewskin.TransactionNotificationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <activity
            android:name=".ui.activities.inforekeningnewskin.TransactionNotificationSettingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <activity
            android:name=".ui.activities.FilterAktivitasActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize"/>

        <activity
            android:name=".ui.activities.estatement.EStatementActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeNewSkinFull"
            android:windowSoftInputMode="stateVisible|adjustResize"/>

        <activity
            android:name=".ui.activities.detailkartunewskin.ChangePINDebitNewSkinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppThemeBlueBarNewSkin"
            android:windowSoftInputMode="adjustResize"/>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_stat_notif" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notif_color" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_key" />
        <meta-data
            android:name="firebase_performance_logcat_enabled"
            android:value="true" />

        <provider
            android:name=".domain.providers.GenericFileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <service
            android:name=".domain.firebase.BrimoFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </service> <!-- AWS S3 Service -->
        <service
            android:name="com.amazonaws.mobileconnectors.s3.transferutility.TransferService"
            android:enabled="true" />

        <receiver
            android:name=".domain.ConnectionReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".ui.widget.CopyActionReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.example.COPY_ACTION" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".ui.widget.BrimoAppsWidget"
            android:exported="false">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="android.appwidget.action.APPWIDGET_ENABLED" />
                <action android:name="android.appwidget.action.APPWIDGET_DELETED" />
                <action android:name="android.appwidget.action.APPWIDGET_DISABLED" />
                <!-- Add this action to listen for system configuration changes -->
                <action android:name="android.intent.action.CONFIGURATION_CHANGED" />
                <action android:name="android.intent.action.PACKAGE_DATA_CLEARED" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/widget_info" />
        </receiver>

        <service
            android:name=".nfcpayment.NFCPaymentService"
            android:exported="true"
            android:permission="android.permission.BIND_NFC_SERVICE">
            <intent-filter>
                <action android:name="android.nfc.cardemulation.action.HOST_APDU_SERVICE" />
            </intent-filter>

            <meta-data
                android:name="android.nfc.cardemulation.host_apdu_service"
                android:resource="@xml/apduservice" />
        </service>
    </application>

</manifest>