package id.co.bri.brimo.presenters.pln;

import id.co.bri.brimo.contract.IPresenter.pln.IInquiryPlnTokenPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimo.contract.IView.pln.IInquiryPlnTokenView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.base.BaseInquiryPresenter;

import io.reactivex.disposables.CompositeDisposable;

public class InquiryplnTokenPresenter<V extends IMvpView & IBaseInquiryView & IInquiryPlnTokenView> extends BaseInquiryPresenter<V> implements IInquiryPlnTokenPresenter<V> {

    public InquiryplnTokenPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }
}
