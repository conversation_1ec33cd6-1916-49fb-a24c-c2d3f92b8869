package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingSendKycReq

interface IOnboardingCameraPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId() : String

    fun setUrlCheckKtp(url: String)

    fun setUrlKycKtp(url: String)

    fun sendCheckKtp()

    fun sendKycKtp(onboardingSendKycReq: OnboardingSendKycReq)
}