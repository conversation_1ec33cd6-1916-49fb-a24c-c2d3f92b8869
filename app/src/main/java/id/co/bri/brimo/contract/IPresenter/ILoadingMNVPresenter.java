package id.co.bri.brimo.contract.IPresenter;

import android.content.Context;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.mnv.CheckMNVReq;
import id.co.bri.brimo.models.apimodel.request.PinRefnumRequest;

public interface ILoadingMNVPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlReqMNV(String urlReqMNV);

    void setUrlCheckMNV(String urlCheckMNV);

    void onSendReqMNV(PinRefnumRequest request);

    void onMNVCheck(CheckMNVReq request);

    void setConnectionMnv(Context ctx, String url, String refNum, Integer maxRetry);

    void registerNetworkCallback(Context context, String refNum);

    public void unregisterNetworkCallback();
}
