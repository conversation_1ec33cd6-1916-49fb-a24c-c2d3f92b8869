package id.co.bri.brimo.presenters.dompetdigitalreskin

import android.util.Log
import id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin.IFormDompetDigitalReskinPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IFormDompetDigitalReskinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.dompetdigitalrevamp.InquiryDompetDigitalRequest
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductListResponse
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceListResponse
import id.co.bri.brimo.presenters.base.BaseFormRevampPresenter
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.models.apimodel.request.UpdateSavedRequest
import id.co.bri.brimo.models.apimodel.response.BaseFormResponse
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralFormResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.DompetDigitalResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.presenters.base.BaseTransactionPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

/**
 * Consolidated presenter that combines FormDompetDigitalRevamp, InputNomor, and wallet binding functionality
 * for FormDompetDigitalReskinActivity
 */
class FormDompetDigitalReskinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource,
): BaseTransactionPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IFormDompetDigitalReskinPresenter<V> where V : IMvpView, V: IFormDompetDigitalReskinView {

    // URL methods are inherited from BaseFormRevampPresenter
    private lateinit var urlEwalletBindingList: String
    private lateinit var urlEwalletBalance: String
    private var isAnyEwalletBinded = false

    lateinit var mUrlForm: String
    lateinit var mUrlInquiry: String
    lateinit var mUrlConfirm: String
    lateinit var mUrlPayment: String

    private lateinit var baseFormResponse: BaseFormResponse
    private var isFirstTime = true

    override fun getDataInquirySaved(isFromFastMenu: Boolean, walletCode: String, corpCode: String, purchaseNumber: String) {
        if (!isViewAttached()) return

        val inquiryRequest = if (isFromFastMenu)
            InquiryDompetDigitalRequest(
                brImoPrefRepository.username,
                brImoPrefRepository.tokenKey,
                walletCode,
                corpCode,
                purchaseNumber
            )
        else
            InquiryDompetDigitalRequest(
                walletCode,
                corpCode,
                purchaseNumber
            )

        val seqNum = getBRImoPrefRepository().getSeqNumber()

        getView().showProgress()
        getCompositeDisposable()
            .add(getApiSource().getData(getUrlInquiry(), inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {

                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val inquiryResponse = response.getData(InquiryDompetDigitalResponse::class.java)
                        getView().onSuccessGetInquirySaved(inquiryResponse, getUrlConfirm(), getUrlPayment(), isFromFastMenu, purchaseNumber)
                    }

                    override fun onApiCallError(response: RestResponse) {
                        getView().hideProgress()
                        onApiError(response)
                    }
                }))
    }

    override fun getDataForm() {
        if (mUrlForm.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
//        getView().showProgress()
        compositeDisposable.add(
            apiSource.getDataForm(mUrlForm, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
//                        getView().hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
//                        getView().hideProgress()
                        if (response.data != null) onApiSuccess(response)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
//                        getView().hideProgress()
                        if (restResponse.code == RestResponse.ResponseCodeEnum.RC_FO.value) {
                            val response = restResponse.getData(EmptyStateResponse::class.java)
                            view.onExceptionFO(response)
                        } else {
                            onApiError(restResponse)
                        }
                    }
                })
        )
    }

    override fun getDataFormSilent() {
        if (mUrlForm.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        // No showProgress() call here to avoid multiple loading states
        compositeDisposable.add(
            apiSource.getDataForm(mUrlForm, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        // No hideProgress() call since we didn't show progress
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        // No hideProgress() call since we didn't show progress
                        if (response.data != null) onApiSuccess(response)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        // No hideProgress() call since we didn't show progress
                        if (restResponse.code == RestResponse.ResponseCodeEnum.RC_FO.value) {
                            val response = restResponse.getData(EmptyStateResponse::class.java)
                            view.onExceptionFO(response)
                        } else {
                            onApiError(restResponse)
                        }
                    }
                })
        )
    }

    override fun getDataFormFastMenu() {
        if (mUrlForm.isEmpty() || !isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

//        getView().showProgress()
        compositeDisposable.add(
            apiSource.getData(mUrlForm, getFastMenuRequest(), seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.single())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
//                        getView().hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
//                        getView().hideProgress()
                        if (response.data != null) onApiSuccess(response)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
//                        getView().hideProgress()
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun getDataInquiry(eWalletCode: String, corpCode: String, purchaseNumber: String) {
        if (!isViewAttached()) return

        val inquiryRequest = InquiryDompetDigitalRequest(eWalletCode, corpCode, purchaseNumber)
        val seqNum = getBRImoPrefRepository().getSeqNumber()

        getView().showProgress()
        getCompositeDisposable()
            .add(getApiSource().getData(getUrlInquiry(), inquiryRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {

                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()
                        val inquiryResponse = response.getData(InquiryDompetDigitalResponse::class.java)
                        getView().onSuccessGetInquiry(inquiryResponse, getUrlConfirm(), getUrlPayment(), false)
                    }

                    override fun onApiCallError(response: RestResponse) {
                        getView().hideProgress()
                        onApiError(response)
                    }
                }))
    }

    override fun getEwalletBindingList() {
        if (!isViewAttached() || !::urlEwalletBindingList.isInitialized || urlEwalletBindingList.isEmpty()) {
            return
        }

        val seqNum = getBRImoPrefRepository().getSeqNumber()
        getCompositeDisposable().add(
            getApiSource().getDataTanpaRequest(urlEwalletBindingList, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {

                    override fun onFailureHttp(errorMessage: String) {
                        getView().onFailedGetEwalletBinding(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        isAnyEwalletBinded = false
                        val ewalletProductListResponse = response.getData(EwalletProductListResponse::class.java)
                        if (ewalletProductListResponse.products != null) {
                            for (i in ewalletProductListResponse.products.indices) {
                                if (ewalletProductListResponse.products[i].isBind == 1) {
                                    isAnyEwalletBinded = true
                                }
                            }
                        }

                        if (!isAnyEwalletBinded || !::urlEwalletBalance.isInitialized || urlEwalletBalance.isEmpty()) {
                            getView().onSuccessGetEwalletBinding(ewalletProductListResponse)
                        } else {
                            getEwalletBalance(ewalletProductListResponse)
                        }
                    }

                    override fun onApiCallError(response: RestResponse) {
                        onApiError(response)
                    }
                })
        )
    }

    override fun getEwalletBalance(ewalletProductListResponse: EwalletProductListResponse) {
        Log.d("FormDompetDigitalPresenter", "=== getEwalletBalance called ===")
        Log.d("FormDompetDigitalPresenter", "urlEwalletBalance initialized: ${::urlEwalletBalance.isInitialized}")
        if (::urlEwalletBalance.isInitialized) {
            Log.d("FormDompetDigitalPresenter", "urlEwalletBalance: $urlEwalletBalance")
        }

        if (!isViewAttached() || !::urlEwalletBalance.isInitialized || urlEwalletBalance.isEmpty()) {
            Log.d("FormDompetDigitalPresenter", "Fallback: calling onSuccessGetEwalletList without balance")
            // Fallback to showing list without balance
            getView().onSuccessGetEwalletList(ewalletProductListResponse)
            return
        }

        Log.d("FormDompetDigitalPresenter", "Making balance API call...")
        val seqNum = getBRImoPrefRepository().getSeqNumber()
        getCompositeDisposable().add(
            getApiSource().getDataTanpaRequest(urlEwalletBalance, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(object : ApiObserver(getView(), seqNum) {

                    override fun onFailureHttp(errorMessage: String) {
                        Log.d("FormDompetDigitalPresenter", "Balance API onFailureHttp: $errorMessage")
                        // Create empty balance response instead of calling without balance
                        val emptyBalanceResponse = EwalletBalanceListResponse()
                        emptyBalanceResponse.balances = ArrayList()
                        getView().onSuccessGetEwalletList(ewalletProductListResponse, emptyBalanceResponse)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        Log.d("FormDompetDigitalPresenter", "Balance API onApiCallSuccess")
                        val ewalletBalanceListResponse = response.getData(EwalletBalanceListResponse::class.java)
                        Log.d("FormDompetDigitalPresenter", "Balance response balances count: ${ewalletBalanceListResponse.balances?.size ?: 0}")
                        getView().onSuccessGetEwalletList(ewalletProductListResponse, ewalletBalanceListResponse)
                    }

                    override fun onApiCallError(response: RestResponse) {
                        Log.d("FormDompetDigitalPresenter", "Balance API onApiCallError: ${response.desc}")
                        // Create empty balance response instead of calling without balance
                        val emptyBalanceResponse = EwalletBalanceListResponse()
                        emptyBalanceResponse.balances = ArrayList()
                        getView().onSuccessGetEwalletList(ewalletProductListResponse, emptyBalanceResponse)
                        onApiError(response)
                    }
                })
        )
    }

    override fun setUrlEwalletBindingList(url: String) {
        urlEwalletBindingList = url
    }

    override fun setUrlEwalletBalance(url: String) {
        urlEwalletBalance = url
    }

    override fun onApiSuccess(response: RestResponse) {
        // Handle successful form data response
        val dompetDigitalResponse = response.getData(DompetDigitalResponse::class.java)
        getView().onSuccessGetForm(dompetDigitalResponse)
    }

    override fun setUpdateItem(url: String, savedResponse: SavedResponse, position: Int, type: Int, journeyType: JourneyType?) {
        if (url.isEmpty() || !isViewAttached || onLoad) {
            return
        }
        view?.showProgress()
        onLoad = true
        val s = savedResponse.value
        val str1 = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        val saveId = str1[0]
        val productId = str1[1]
        val updateSavedRequest = UpdateSavedRequest(saveId, productId)
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getData(url, updateSavedRequest, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        onLoad = false
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        onLoad = false
                        getView().hideProgress()
                        getView().onSuccessUpdate(savedResponse, position, type)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        onLoad = false
                        getView().hideProgress()
                        onApiError(restResponse)
                    }
                })
        )
    }

    override fun setUrlForm(urlForm: String) {
        mUrlForm = urlForm
    }

    override fun setUrlInquiry(urlInquiry: String) {
        mUrlInquiry = urlInquiry
    }

    override fun setUrlConfirm(urlConfirm: String) {
        mUrlConfirm = urlConfirm
    }

    override fun setUrlPayment(urlPayment: String) {
        mUrlPayment = urlPayment
    }

    override fun getUrlInquiry(): String = if (this::mUrlInquiry.isInitialized) mUrlInquiry.ifEmpty { "" } else ""

    override fun getUrlConfirm(): String = if (this::mUrlConfirm.isInitialized) mUrlConfirm.ifEmpty { "" } else ""

    override fun getUrlPayment(): String = if (this::mUrlPayment.isInitialized) mUrlPayment.ifEmpty { "" } else ""

    private fun getBaseFormResponse(): BaseFormResponse {
        return baseFormResponse
    }

    private fun setBaseFormResponse(baseFormResponse: BaseFormResponse) {
        this.baseFormResponse = baseFormResponse
    }
}