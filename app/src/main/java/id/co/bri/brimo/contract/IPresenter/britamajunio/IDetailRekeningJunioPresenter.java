package id.co.bri.brimo.contract.IPresenter.britamajunio;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.britamajunio.IDetailRekeningJunioView;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimo.models.apimodel.request.PinFinansialRequest;

public interface IDetailRekeningJunioPresenter<V extends IMvpView & IDetailRekeningJunioView> extends IMvpPresenter<V>  {

    void setDetailJunio(String accountNumber);

    void setUrlDetailJunio(String url);

    void setUrlStatus(String urlStatus);

    void setUrlDefault(String urlDefault);

    void setUrlFinansialRek(String urlFinansialRek);

    void setUrlInfoSaldoHold(String urlInfoSaldoHold);

    void setUrlDetailStatus(String urlDetail);

    void getInfoSaldoHold();

    void sendFinansialRek(PinFinansialRequest pinRequest, int statusFinansial);

    void setChangeDefault(String accountNumber);

    void getDataDetailStatus(DetailKelolaKartuReq detailKelolaKartuReq);
}