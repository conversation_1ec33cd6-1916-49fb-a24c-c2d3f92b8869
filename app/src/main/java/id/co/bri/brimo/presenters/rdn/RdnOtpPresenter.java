package id.co.bri.brimo.presenters.rdn;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import id.co.bri.brimo.contract.IPresenter.ubahpassword.IUbahPasswordOtpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.rdn.IRdnOtpView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.UbahKunciResendOtpRequest;
import id.co.bri.brimo.models.apimodel.request.UbahKuncValidateOtpRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.UbahPasswordResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;

public class RdnOtpPresenter<V extends IMvpView & IRdnOtpView> extends MvpPresenter<V> implements IUbahPasswordOtpPresenter<V> {

    private String urlValidateOtp = "";
    private String urlValidateResendOtp = "";
    private String otp = "";

    @Inject
    public RdnOtpPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }


    @Override
    public void setUrlValidateOtp(String urlValidateOtp) {
        this.urlValidateOtp = urlValidateOtp;
    }

    @Override
    public void setUrlValidateResendOtp(String validateResendOtp) {
        this.urlValidateResendOtp = validateResendOtp;
    }

    @Override
    public void getOtp(String otp) {
        this.otp = otp;
    }

    @Override
    public void sendOtp() {
        if (getView() == null) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahKuncValidateOtpRequest ubahPasswordRequest = new UbahKuncValidateOtpRequest(getView().getRefNumber(), otp);
        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlValidateOtp, ubahPasswordRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    getView().onSuccessSendOtp();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    getView().onResetOtp();
                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                        getView().onSessionExp(restResponse.getDesc());
                                    }
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }

    @Override
    public void onResendOtpSubmit() {
        if (getView() == null) {
            return;
        }
        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        UbahKunciResendOtpRequest ubahKunciRequest = new UbahKunciResendOtpRequest(getView().getRefNumber());
        if (isViewAttached()) {
            getCompositeDisposable().add(
                    getApiSource().getData(urlValidateResendOtp, ubahKunciRequest, seqNum)
                            .subscribeOn(getSchedulerProvider().single())
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .observeOn(getSchedulerProvider().mainThread())
                            .subscribeWith(new ApiObserver(getView(),seqNum) {
                                @Override
                                protected void onFailureHttp(String type) {
                                    getView().hideProgress();
                                    getView().onException(type);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    UbahPasswordResponse ubahPasswordBaruResponse = response.getData(UbahPasswordResponse.class);
                                    getView().onSuccessResendOtp(ubahPasswordBaruResponse);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();

                                    if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
                                        getView().onSessionEnd(restResponse.getDesc());
                                    }
                                    else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                        getView().onSessionExp(restResponse.getDesc());
                                    }
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            })
            );
        }
    }


}