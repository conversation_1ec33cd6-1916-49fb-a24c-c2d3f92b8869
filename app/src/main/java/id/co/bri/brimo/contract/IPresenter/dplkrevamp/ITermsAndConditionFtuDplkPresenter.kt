package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.PaymentFtuDplkRequest

interface ITermsAndConditionFtuDplkPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlPaymentFtuDplk(url:String)
    fun getDataUrlPaymentFtuDplk(request: PaymentFtuDplkRequest)
}