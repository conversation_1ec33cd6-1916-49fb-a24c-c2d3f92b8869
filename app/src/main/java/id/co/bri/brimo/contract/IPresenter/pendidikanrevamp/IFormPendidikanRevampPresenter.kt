package id.co.bri.brimo.contract.IPresenter.pendidikanrevamp

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.request.InquiryPendidikanRequest
import id.co.bri.brimo.models.apimodel.response.SavedResponse

interface IFormPendidikanRevampPresenter<V : IBaseFormRevampView> : IBaseFormRevampPresenter<V> {
    fun getDataInquiry(inquiryPendidikanRequest: InquiryPendidikanRequest)
}