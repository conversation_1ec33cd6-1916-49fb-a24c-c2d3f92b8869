package id.co.bri.brimo.contract.IPresenter.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.rdn.RdnHistoryListWithdrawRequest

interface IHistoryWithdrawProcessRdnPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlHistoryWithdraw(urlHistory: String)
    fun setUrlHistoryWithdrawDetail(urlHistoryDetail: String)
    fun getHistoryWithdraw(request: RdnHistoryListWithdrawRequest)
    fun getHistoryWithdrawDetail(request: RdnHistoryListWithdrawRequest)
}