package id.co.bri.brimo.contract.IPresenter.transferVallas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IConfirmationKonversiValasPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {
    void getDataPayment (String pin, String note, GeneralConfirmationResponse response, boolean fromFast);

    void setUrlPayment(String urlPayment);

    Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName,String trxType);

    void onSaveTransaksiPfm(Transaksi transaksi);
}
