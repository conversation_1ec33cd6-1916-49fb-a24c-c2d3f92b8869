package id.co.bri.brimo.contract.IPresenter.listrikrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest

interface ITambahDaftarListrikRevampPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDataInquiry(request: InquiryPlnRequest)
    fun getDataInquiryAddSavedList(inquiryPlnRequest: InquiryPlnRequest);
    fun setInquiryUrl(url: String?)
}