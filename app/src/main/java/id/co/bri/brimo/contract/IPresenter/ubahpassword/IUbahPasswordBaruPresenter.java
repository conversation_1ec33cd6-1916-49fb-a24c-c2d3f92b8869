package id.co.bri.brimo.contract.IPresenter.ubahpassword;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IUbahPasswordBaruPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void onUbahPasswordSubmit();

    void setUrlValidatePassKunci(String string);

    void setUrlValidateOldPass(String string);

    void confirmNewPass();

    Boolean checkInputUser();

}
