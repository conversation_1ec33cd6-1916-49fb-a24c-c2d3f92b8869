package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.BranchRequest
import id.co.bri.brimo.models.apimodel.request.bukarekening.KonfirmasiTabunganRequest

interface IInquiryProductTabunganRevPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlKonfirmasi(url : String)

    fun setUrlPilihKode(url : String)

    fun getKonfirmasi(request: KonfirmasiTabunganRequest)

    fun getBranchCode(request: BranchRequest)

}