package id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IInquiryDompetDigitalReskinPresenter  <V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlConfirm(urlConfirm: String)

    fun getDataConfirmation(refNum: String, accountNum: String, amount: String, save: String, note : String, fromFast: Boolean)

    fun getAccountDefault():String

    fun getSaldoRekeningUtama():String
}