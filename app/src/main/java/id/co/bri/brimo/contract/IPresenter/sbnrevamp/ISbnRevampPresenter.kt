package id.co.bri.brimo.contract.IPresenter.sbnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.esbn.RegisKemenkeuRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnDetailPortoRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnSimulasiRequest

interface ISbnRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlSbnDashboardHeader(urlDashboardHeader: String)

    fun setUrlSbnDashboardBody(urlDashboardBody: String)

    fun setUrlBeliSbn(urlBeliSbn : String)

    fun setUrlRegistrasiSbn(urlRegisSbn : String)

    fun setUrlProductsBrief(urlProductBrief: String)

    fun setUrlSimulasiSbn(url : String)

    fun setUrlSbnDetailPorto(urlDetailItemPorto: String)

    fun setUrlGetDetail(urlGetDetail: String)

    fun setUrlValidateUsers(urlValidate : String)

    fun setUrlKemenkeu(urlKemenkeu : String)

    fun getSbnDashboardHeader()

    fun getSbnDashboardBody()

    fun getSbnBeliSbn()

    fun getSbnRegisData()

    fun getOnProductBrief()

    fun getDataSimulasiSbn(request : SbnSimulasiRequest,idSeri: Int)

    fun getDataDetailPortoSbn(request : SbnDetailPortoRequest)

    fun getDetailOffer(idSeri: Int)

    fun getValidateUser(idSeri: Int)

    fun getValidateUserLihatSemua(idSeri: Int)

    fun saveBackGeneral(status: Int)

    fun getKemenkeuData(regisKemenkeuRequest: RegisKemenkeuRequest)

}