package id.co.bri.brimo.contract.IPresenter.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnInquiryWithdrawRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnNewDetailRequest

interface IDashboardRdnRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlDashboardRevamp(urlDashboard: String)
    fun setUrlDetailRdn(urlDetail: String)
    fun setUrlWithdrawRdn(urlWithdrawRdn: String)
    fun setUrlRegisterRdn(urlRegisterRdn: String)
    fun setUrlTopUpRdn(urlTopUpRdn: String)
    fun setBubbleDashboardRdnRevamp(isShowingBubble: Boolean)
    fun getBubbleDashboardRdnRevamp(): Boolean
    fun getDataDashboardRevamp()
    fun getDataDetailRdn(request: RdnNewDetailRequest)
    fun getWithdrawRdn(request: RdnInquiryWithdrawRequest)
    fun getDataRegisterRdn()
    fun getTopUpRdn(request: InquiryRdnRequest)
}