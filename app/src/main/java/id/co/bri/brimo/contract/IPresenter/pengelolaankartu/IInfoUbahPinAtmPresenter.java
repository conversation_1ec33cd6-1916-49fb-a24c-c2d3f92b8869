package id.co.bri.brimo.contract.IPresenter.pengelolaankartu;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.PinBrimoRequest;

public interface IInfoUbahPinAtmPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlPinBrimo(String urlPinBrimo);

    void sendDataPinBrimo(PinBrimoRequest request);
}
