package id.co.bri.brimo.contract.IPresenter.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IOnBoardingVDCPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrlGetListVirtualCard(url : String)

    fun getListVirtualCard(username : String)

    fun setUrlGetListAccount(url : String)

    fun getListAccount(username : String)

    fun setUrlGetListProductVDC(url : String)

    fun getListProductVDC(account : String)

    fun setUrlCheckStatusCreateVDC(url : String)

    fun checkStatusCreateVDC(account : String)

    fun setUrlDetailVDC(url : String)

    fun getDetailVDC(cardNumber : String)
}