package id.co.bri.brimo.contract.IPresenter;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.daomodel.RateUs;

public interface IRateUsBottomPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlSendRate(String urlRateUs);

    void onSendDataRate(String rating, String message, String tag, String date, long counter, long id);

    void onUpdateRateDb(long rating, String date, long counter, long id);

    void onSaveRateDb(RateUs rateUs);
}