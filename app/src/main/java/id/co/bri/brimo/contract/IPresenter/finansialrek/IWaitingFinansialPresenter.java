package id.co.bri.brimo.contract.IPresenter.finansialrek;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.ResendOtpRequest;

public interface IWaitingFinansialPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlSend(String urlSend);

    void setUrlResend(String urlResend);

    void onConfirmWaiting(String code);

    void onResendWaiting(ResendOtpRequest resendOtpRequest);
}
