package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisKycRequest

interface IRegistrasiVerifyWajahPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId() : String

    fun setUrlVideo(url: String)

    fun sendVideoRegis(regisKycRequest: RegisKycRequest)
}