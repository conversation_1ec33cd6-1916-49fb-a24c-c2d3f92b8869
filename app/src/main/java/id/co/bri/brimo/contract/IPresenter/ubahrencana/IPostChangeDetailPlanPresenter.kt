package id.co.bri.brimo.contract.IPresenter.ubahrencana

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.PostChangeDetailPlanRequest

interface IPostChangeDetailPlanPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrlPostChangeDetailPlan(url: String)

    fun postChangeDetailPlanRequest(postChangeDetailPlanRequest: PostChangeDetailPlanRequest)
}