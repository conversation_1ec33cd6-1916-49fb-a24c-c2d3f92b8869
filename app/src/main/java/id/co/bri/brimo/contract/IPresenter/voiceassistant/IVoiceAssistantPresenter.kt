package id.co.bri.brimo.contract.IPresenter.voiceassistant

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.voiceassistant.VoiceAssistantRequest

interface IVoiceAssistantPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun getTokenKey(): String
    fun getUsername(): String
    fun getNickname(): String
    fun setUrlVoiceAssistant(url: String)
    fun sendTextToVoiceAssistant(voiceAssistantRequest: VoiceAssistantRequest)

}