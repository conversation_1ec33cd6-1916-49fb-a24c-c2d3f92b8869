package id.co.bri.brimo.contract.IPresenter.regisEsbn;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.esbn.KonfirmasiRdnSbnRequest;

public interface IEsbnKonfirmasiFromRdnPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void setUrl(String Url);

    void onGetTtdDigital(KonfirmasiRdnSbnRequest konfirmasiRdnSbnRequest);

}
