package id.co.bri.brimo.contract.IPresenter.pengelolaankartu;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ReissuePinRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.UbahPinAtmRequest;

public interface IUbahPinAtmPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void sendChangePin(UbahPinAtmRequest request);

    void sendReissuePin(ReissuePinRequest request);
}
