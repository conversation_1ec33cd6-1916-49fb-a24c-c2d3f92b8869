package id.co.bri.brimo.contract.IPresenter.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnInquiryWithdrawRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnNewDetailRequest

interface IOnboardingRdnRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlRegisterRdn(urlRegisterRdn: String)
    fun getDataRegisterRdn()
}