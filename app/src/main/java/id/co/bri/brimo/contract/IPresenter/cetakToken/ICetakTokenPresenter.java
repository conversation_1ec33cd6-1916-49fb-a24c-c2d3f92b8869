package id.co.bri.brimo.contract.IPresenter.cetakToken;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface ICetakTokenPresenter <V extends IMvpView>
        extends IMvpPresenter<V>{

    void setUrlCetak(String urlCetak);

    void setUrlDetailCetak(String urlDetailCetak);

    void getCetakToken(String lastID,boolean isRefresh);

//    void getInbox(String periode, String status, String fitur, String subFitur, String lastId, boolean isRefresh);

    void getCetakDetail(String refnumber);
}