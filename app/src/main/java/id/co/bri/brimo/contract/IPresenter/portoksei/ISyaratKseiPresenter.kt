package id.co.bri.brimo.contract.IPresenter.portoksei

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.portofolioksei.GetDetailKseiRequest
import id.co.bri.brimo.models.apimodel.request.portofolioksei.SyaratKseiRequest

interface ISyaratKseiPresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun setUrl(url: String)

    fun getDataPin(req : SyaratKseiRequest)
}