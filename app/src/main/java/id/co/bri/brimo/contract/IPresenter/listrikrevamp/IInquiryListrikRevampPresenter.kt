package id.co.bri.brimo.contract.IPresenter.listrikrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.ConfirmationRequest
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest

interface IInquiryListrikRevampPresenter <V : IMvpView> : IMvpPresenter<V> {
    fun getDataConfirmation(request: ConfirmationRequest)
    fun setUrlConfirmation(url: String?)
    fun getAccountDefault():String
    fun getSaldoRekeningUtama():String
}