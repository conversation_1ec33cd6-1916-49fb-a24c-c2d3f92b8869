package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisPinRequest

interface IRegistrasiConfirmPinPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId(): String

    fun setUrlConfirmPin(url: String)

    fun sendConfirmPin(regisPinRequest: RegisPinRequest)
}