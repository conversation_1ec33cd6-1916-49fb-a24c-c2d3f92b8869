package id.co.bri.brimo.contract.IPresenter.sbnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.esbn.RegisKemenkeuRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnSimulasiRequest

interface ISimulasiSbnPresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlSimulasiSbn(url: String)

    fun setUrlBeliSbn(urlBeliSbn : String)

    fun setUrlValidateUser(urlValidate : String)

    fun setUrlKemenkeu(urlKemenkeu : String)

    fun setUrlRegistrasiSbn(urlRegisSbn : String)

    fun setUrlProductsBrief(urlProductBrief: String)
    fun getDataSimulasiSbn(request: SbnSimulasiRequest)

    fun getDetailOffer(idSeri: Int)

    fun getValidateUser(idSeri: Int)

    fun getKemenkeuData(regisKemenkeuRequest: RegisKemenkeuRequest)

    fun getSbnRegisData()

    fun getOnProductBrief()

}