package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisUserPassRequest
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisUsernameRequest

interface IRegistrasiUserPassPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId()

    fun setUrlUsername(url: String)

    fun sendUserCheck(regisUsernameRequest: RegisUsernameRequest)

    fun setUrlUserPass(url: String)

    fun sendUserPass(regisUserPassRequest: RegisUserPassRequest)
}