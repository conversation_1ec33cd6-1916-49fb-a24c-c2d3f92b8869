package id.co.bri.brimo.contract.IPresenter.splitbill

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.splitbill.SplitBillAddMemberItemViewModel

interface ISplitBillAddMemberPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun fetchIntentChosenMembers(
        addMembers: List<SplitBillAddMemberItemViewModel>,
        recentMembers: List<SplitBillAddMemberItemViewModel>
    )
    fun fetchRecentMembers()
    fun deleteAddedMember(model: SplitBillAddMemberItemViewModel)
    fun addFromRecentMembers(model: SplitBillAddMemberItemViewModel)

    fun doFilterRecentMembers(searchQuery: String)

    fun getChosenMembersJson(): String

    fun setUrlInquiryMember(urlInquiryMember: String)

    fun setUrlAddedNewMember(urlNewMember: String)

    fun getInquiryMember(newMemberInput: String)

    fun getAddedNewMember()

    fun checkIsAddNewMember()

    fun onNewMemberSubmitted(newMemberInput: String)
}