package id.co.bri.brimo.contract.IPresenter.splitbill

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.splitbill.MarkPaymentStatusResponse
import id.co.bri.brimo.models.splitbill.SplitBillAddMemberItemViewModel

interface ISplitBillDetailPerMemberPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun fetchData(
        billId: Int,
        memberItemViewModel: SplitBillAddMemberItemViewModel,
        isCreator: Boolean,
        isOtherParticipant: Boolean
    )

    fun updateMemberPaymentStatus(
        markPaymentStatusResponse: MarkPaymentStatusResponse,
        isPaidOff: Boolean
    )

    fun setUrlMarkPaymentStatus(urlMarkPay: String)

    fun getDataMarkPaymentStatus(mIsPaidOff: <PERSON>ole<PERSON>)
}
