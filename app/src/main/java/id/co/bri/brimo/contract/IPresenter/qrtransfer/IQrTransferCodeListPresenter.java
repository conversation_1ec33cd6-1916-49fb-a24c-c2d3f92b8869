package id.co.bri.brimo.contract.IPresenter.qrtransfer;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IQrTransferCodeListPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void getData(boolean isNewQr, String deletedMpan);

    void getDataPayment(String pin, String account, String amount, String deletedMpan);

    void setFormUrl(String formUrl);

    void setGenerateUrl(String generateUrl);
}
