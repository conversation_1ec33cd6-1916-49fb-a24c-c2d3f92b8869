package id.co.bri.brimo.contract.IPresenter.splitbill

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.splitbill.ConfirmationSplitBillResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.SplitBillDetailResponse

interface ISplitBillConfirmationPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun parseDataFromHistory(splitBillDetailResponse: SplitBillDetailResponse)

    fun fetchData(confirmationSplitBillResponse: ConfirmationSplitBillResponse)

    fun updateDestinationAccount(accountNumber: String, accountName: String)

    fun setUrlDraftConfirm(urlDraftConfirm: String)

    fun setUrlGenerate(urlGenerate: String)

    fun setUrlHistoryBill(urlHistory: String)

    fun getDraftConfirm()

    fun getGenerateBill()

    fun onBackFromHistory()

}
