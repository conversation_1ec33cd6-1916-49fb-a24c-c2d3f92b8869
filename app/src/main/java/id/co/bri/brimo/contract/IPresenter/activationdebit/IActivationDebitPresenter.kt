package id.co.bri.brimo.contract.IPresenter.activationdebit

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.activationdebit.request.ValidateActivationDebitRequest

interface IActivationDebitPresenter<V: IMvpView>: IMvpPresenter<V> {
    fun getActivationDebit(urlActivationDebit: String, validateActivationDebitRequest: ValidateActivationDebitRequest?)
}