package id.co.bri.brimo.contract.IPresenter.britamajunio;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.junio.PaymentJunioRequest;

public interface IConfirmationJunioPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void setaurl(String urlPayment);

    void getPayment(PaymentJunioRequest request);

}
