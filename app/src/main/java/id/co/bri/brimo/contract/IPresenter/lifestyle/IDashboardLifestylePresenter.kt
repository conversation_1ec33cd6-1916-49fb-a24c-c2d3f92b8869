package id.co.bri.brimo.contract.IPresenter.lifestyle

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.response.lifestyle.FeatureDataView
import id.co.bri.brimo.models.daomodel.lifestyle.MenuLifestyle

interface IDashboardLifestylePresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlBagdeTrx(urlBadgeTrx: String)

    fun setUrlDashboardInformation(urlInfo: String)

    fun setUrlDashboardSelling(urlSelling: String)

    fun setUrlWebviewTugu(urlTugu: String)

    fun setUrlBusShuttle(urlFormBus: String)

    fun setUrlKai(urlFormKai: String)

    fun getFormBus()

    fun getFormKai(titleBar: String)

    fun getBadgeTrx()

    fun getDashboardInformation()

    fun getDashboardSelling()

    fun getWebViewTugu(partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String)

    fun getMenuLifestyleLocal()

    fun setFourMenu(list: List<FeatureDataView>,
                    indexStart: Int,
                    indexEnd: Int
    ): List<FeatureDataView>

    fun setFilteredMenu(listMenu: List<FeatureDataView>): List<FeatureDataView>

    fun setFilteredMenuNew(listMenu: List<MenuLifestyle>): List<MenuLifestyle>

    fun getNewMenubyParentCode(featureCode: String)

    fun getIndihomeRegistrationData(selectedMenu: FeatureDataView)
    fun confirmIndihomeRegistration(selectedMenu: FeatureDataView)

}