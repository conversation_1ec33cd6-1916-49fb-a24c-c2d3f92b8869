package id.co.bri.brimo.contract.IPresenter;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.GeneralValidateOtpRequest;

public interface IOtp4DigitDefaultPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlSend(String urlSend);

    void setUrlResend(String urlResend);

    void onSendData(GeneralValidateOtpRequest request);

    void onResendData(String refNum);
}
