package id.co.bri.brimo.contract.IPresenter.lupausername;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ResendOtpReissueReq;

public interface IVerifikasiOtpNoHpPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlValidate(String url);

    void setUrlResend(String url);

    void resendOtp(ResendOtpReissueReq resendOtpReissueReq);

    void validateOtp(ValidateOtpUserPassReq request);
}
