package id.co.bri.brimo.contract

class MasukkanPinContract {

    interface View : BaseContract.View<Presenter> {
        /**
         * If the pin input is correct
         */
        fun onCorrectPin()

        /**
         * If the pin input is wrong
         */
        fun onWrongPin()
    }

    interface Presenter : BaseContract.Presenter {
        /**
         * Check the full pin in presenter
         */
        fun checkPin(pin: String)
    }
}