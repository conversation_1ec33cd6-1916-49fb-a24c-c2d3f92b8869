package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisEmailRequest

interface IRegistrasiVerifyEmailPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlResend(url: String)

    fun setUrlCheckProgress(url: String)

    fun sendResend(regisEmailRequest: RegisEmailRequest)

    fun sendCheckProgress()
}