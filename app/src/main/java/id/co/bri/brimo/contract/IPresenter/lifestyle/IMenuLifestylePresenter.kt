package id.co.bri.brimo.contract.IPresenter.lifestyle

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.response.lifestyle.FeatureDataView

interface IMenuLifestylePresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun onUpdateFlagNewMenu(featureCode: String)

    fun setUrlBusShuttle(urlFormBus: String)

    fun setUrlKai(urlFormKai: String)

    fun setUrlWebviewTugu(urlTugu: String)

    fun getFormBus()

    fun getFormKai(titleBar: String)

    fun getWebViewTugu(partnerIdRequest: PartnerIdRequest?,
                       titleBar: String,
                       codeMenu: String
                       )

    fun getMenuLifestyle()
    fun getIndihomeRegistrationData(selectedMenu: FeatureDataView)
    fun confirmIndihomeRegistration(selectedMenu: FeatureDataView)
}