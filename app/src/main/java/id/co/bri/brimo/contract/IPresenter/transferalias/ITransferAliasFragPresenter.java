package id.co.bri.brimo.contract.IPresenter.transferalias;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.transferalias.ITransferAliasFragView;
import id.co.bri.brimo.contract.IView.base.IBaseFormNosavedView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRequest;

public interface ITransferAliasFragPresenter<V extends IMvpView & IBaseFormNosavedView & IBaseFormView & ITransferAliasFragView>
        extends IBaseFormPresenter<V> {
    void getDataInquiry(InquiryTransferAliasRequest inquiryTransferAliasRequest, boolean isFromFastMenu);
}