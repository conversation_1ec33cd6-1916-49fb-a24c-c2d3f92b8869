package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiInternasionalRequest;
import id.co.bri.brimo.models.apimodel.request.PaymentRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiInternasionalResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IKonfirmasiIntlPresenter <V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void getPaymentInternational(PaymentRequest paymentRequest, KonfirmasiInternasionalResponse konfirmasiInternasionalResponse);

    void isGeneral(boolean general);

    Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName);

    void onSaveTransaksiPfm(Transaksi transaksi);
}
