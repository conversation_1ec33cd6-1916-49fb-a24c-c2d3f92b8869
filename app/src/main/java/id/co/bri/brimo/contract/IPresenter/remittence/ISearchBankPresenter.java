package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.JalurRequest;
import id.co.bri.brimo.models.apimodel.request.SearchBankRequest;

public interface ISearchBankPresenter <V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void getListBank(SearchBankRequest searchBankRequest);
}
