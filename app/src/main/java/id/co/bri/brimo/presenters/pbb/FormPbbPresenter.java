package id.co.bri.brimo.presenters.pbb;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.pbb.IFormPbbPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.pbb.IFormPbbView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ReInquiryPbbRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralFormResponse;
import id.co.bri.brimo.models.apimodel.response.ReInquiryPbbResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class FormPbbPresenter<V extends IMvpView & IBaseFormView & IFormPbbView> extends BaseFormPresenter<V> implements IFormPbbPresenter<V> {
    private static final String TAG = "FormWalletPresenter";

    protected String formUrlPbb;
    protected String inquiryUrl;
    protected String reInquiryUrl;

    public FormPbbPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }


    @Override
    public void getDataReInquiry(ReInquiryPbbRequest request, boolean fromFastMenu) {
        if (reInquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();

        inquiryRequest = request;

        String seqNum = getBRImoPrefRepository().getSeqNumber();

        getCompositeDisposable()
                .add(getApiSource().getData(reInquiryUrl, inquiryRequest, seqNum)
                        .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().hideProgress();
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                try {
                                    ReInquiryPbbResponse reInquiryPbbResponse = response.getData(ReInquiryPbbResponse.class);

                                    if (reInquiryUrl != null && konfirmasiUrl != null)
                                        getView().onSuccessGetReInquiry(reInquiryPbbResponse, konfirmasiUrl, paymentUrl, fromFastMenu);
                                }catch (Exception e){
                                }

                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                                getView().hideProgress();
                                if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                    getView().onSessionEnd(restResponse.getDesc());
                                else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_99.getValue()))
                                    getView().onException99(restResponse.getDesc());
                                else
                                    getView().onException(restResponse.getDesc());

                            }
                        }));
    }

    /*
     * method untuk memproses restResponse jika berhasil mendapatkan balikan dari getDataForm() dan getDataFormFastMenu()
     */
    @Override
    public void onApiSuccessFormPbb(RestResponse response) {
        super.onApiSuccess(response);
        if(!isViewAttached())
            return;

        generalFormResponse = response.getData(GeneralFormResponse.class);

        getView().onSuccessGetRestResponse(response);
    }

    @Override
    public void setReInquiryUrl(String url) {
        this.reInquiryUrl = url;
    }


}