package id.co.bri.brimo.contract.IPresenter.lifestyle

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest

interface IRepurchaseLifestylePresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlBusShuttle(urlFormBus: String)

    fun setUrlKai(urlFormKai: String)

    fun setUrlWebviewTugu(urlWebTugu: String)

    fun getFormBus()

    fun getFormKai(mTitle: String)

    fun getWebviewTugu(partnerIdRequest: PartnerIdRequest, mTitle: String)

}