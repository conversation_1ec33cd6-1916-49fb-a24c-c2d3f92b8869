package id.co.bri.brimo.contract.IPresenter.transactionlimitinformation

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface ITransactionLimitInformationPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlGetSafetyMode(url : String)

    fun getSafetyMode()

    fun setUrlGetHelpSafetyMode(url : String)

    fun getHelpSafetyMode(id: String)

    fun setUrlGetLimitInformation(url: String)

    fun getLimitInformation()
}