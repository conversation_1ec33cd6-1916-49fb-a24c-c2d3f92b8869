package id.co.bri.brimo.contract.IPresenter.donasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.donasirevamp.InquiryDonasiRevampRequest

interface IListLembagaRevampPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun getDataInquiry(inquiryRequest: InquiryDonasiRevampRequest)

    fun setUrl(url:String)
}