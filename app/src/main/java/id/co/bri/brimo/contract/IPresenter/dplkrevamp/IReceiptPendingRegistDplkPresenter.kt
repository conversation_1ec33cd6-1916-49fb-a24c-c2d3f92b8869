package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.CheckStatusDplkRequest

interface IReceiptPendingRegistDplkPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlCheckStatusDplk(url: String)

    fun getCheckStatusDplk(request: CheckStatusDplkRequest)
}