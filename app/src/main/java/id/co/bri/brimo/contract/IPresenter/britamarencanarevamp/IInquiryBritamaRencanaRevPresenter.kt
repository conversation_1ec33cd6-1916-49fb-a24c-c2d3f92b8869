package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.rencanarev.ConfirmationRencanaRevRequest

interface IInquiryBritamaRencanaRevPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlKonfirmasi(url : String)

    fun getKonfirmasi(request: ConfirmationRencanaRevRequest)

}