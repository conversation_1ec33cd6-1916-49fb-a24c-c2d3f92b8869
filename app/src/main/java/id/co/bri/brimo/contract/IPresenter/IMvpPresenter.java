package id.co.bri.brimo.contract.IPresenter;



import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastMenuRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;

import io.reactivex.disposables.CompositeDisposable;

public interface IMvpPresenter<V extends IMvpView> {

    void start();

    void stop();

    void setView(V view);

    V getView();

    boolean isViewAttached();

    SchedulerProvider getSchedulerProvider();

    ApiSource getApiSource();

    CompositeDisposable getCompositeDisposable();

    TransaksiPfmSource getTransaksiPfmSource();


    /**
     * Method utk mendapatkan preferrernce
     * @return
     */
    BRImoPrefSource getBRImoPrefRepository();

    //when call API error
    void onApiError(RestResponse restResponse);

    //when call API success
    void onApiSuccess(RestResponse response);

    //when call API got error HTTP
    void onFailure(String error);

    //get default request fastmenu
    FastMenuRequest getFastMenuRequest();

    /**
     * Method digunakan untuk mendisable pop up notification
     * @param disableNotif flag boolean notification
     */
    void setDisablePopup(boolean disableNotif);

    /**
     * Method untuk memvalidasi request API
     * @param value string request
     * @return boolean result
     */
    boolean validateValueRequest(String value);

    /**
     * Method untuk memvalidasi usernname
     * @param value string username
     * @return boolean result
     */
    boolean validateValueUsername(String value);

    /**
     *
     * Method untuk update flag login
     * @param isLogin flag
     *
     */
    void updateLoginFlag(boolean isLogin);

    /**
     *
     * Method utk mengecek status login
     * @return isLogin boolean flag
     *
     */
    boolean getLoginFlag();

    String getPersistenceId();

    String getLanguage();

}
