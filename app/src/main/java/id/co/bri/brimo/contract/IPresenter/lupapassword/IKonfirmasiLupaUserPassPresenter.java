package id.co.bri.brimo.contract.IPresenter.lupapassword;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.PreSumRequest;

public interface IKonfirmasiLupaUserPassPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlReissue(String url);

    void setUrlOtpForgetUserPass(String url);

    void sendOTPReissue();

    void sendOtpForgetUserPass(PreSumRequest request);
}