package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingSendKycReq

interface IOnboardingVerifyWajahPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId(): String

    fun setUrlVideo(url: String)

    fun sendVideoOnboard(onboardingSendKycReq: OnboardingSendKycReq)

    fun checkAdditionalImages(additionalImages: MutableList<ByteArray>?)
}