package id.co.bri.brimo.contract.IPresenter.pengelolaankartu;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.BlokirRequest;
import id.co.bri.brimo.models.apimodel.request.blockcard.BlockCardRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.AccountBindingReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.EnableDisableTransactionRequest;

public interface IDetailKelolaKartuPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlStatusCard(String urlStatusCard);

    void setBlokirKartu(BlokirRequest blokirRequest);

    void setUrlTransactionStatus(String url);

    void setUrlAccountBinding(String urlAccountBinding);

    void enableDisableTransactionStatus(EnableDisableTransactionRequest request);

    void setUrlDetailCard(String url);

    void getDataDetailCard(DetailKelolaKartuReq detailKelolaKartuReq, String snackbarResponse);

    void setAccountBinding(AccountBindingReq accountBindingReq);

    void getChangePinRefNum(String url, String cardNumber);

    void setUrlBlockCard(String urlBlockCard);

    void setBlockCard(BlockCardRequest request);
}