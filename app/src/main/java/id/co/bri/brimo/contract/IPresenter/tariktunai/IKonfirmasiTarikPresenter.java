package id.co.bri.brimo.contract.IPresenter.tariktunai;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiTarikTunaiResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IKonfirmasiTarikPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataPaymentTarik (String pin, String note, KonfirmasiTarikTunaiResponse response);

    void setUrlPayment(String urlPayment);

    Transaksi generateTransaksiModel(int kategoriId, int amount, String referenceNumber, String billingName);

    void onSaveTransaksiPfm(Transaksi transaksi);

    void setDisablePopup(boolean disableNotif);

}
