package id.co.bri.brimo.contract.IPresenter.brivarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse

interface IInquiryKonfirmasiBrivaRevampClosePresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDefaultSaldo()

    fun setUrlPayment(url: String)

    fun getDataPaymentRevamp(pin: String, mInquiryBrivaRevampResponse: InquiryBrivaRevampResponse,
                             account: String, saveAs: String, note: String, fromFastMenu: Boolean)

    fun setIdPayment(id: Int)

    fun setUrlGetCashback(url: String)

    fun getCashbackAll(accountNumber: String, referenceNumber: String, isFromFastMenu: Boolean)

    fun setRedeemCashbackUrl(url: String)

    fun getRedeemCashback(refNum: String, code: String, fromFastMenu: Boolean)
    /*auto grab fund transaction*/
    fun getDataPaymentRevampAgf(pin: String,
                                refNum: String,
                                accountNum: String,
                                pfmCategory: String,
                                agfDateStart: String,
                                agfDateStop: String,
                                agfFrequency: String,
                                agfType: String,
                                saveAs: String,
                                note: String)

    fun setUrlPaymentAgf(url: String)
    fun getIsFirstTimeShowAgf(page: String): Boolean
    fun setIsFirstTimeShowAgf(page: String, isFirstTime: Boolean)

}