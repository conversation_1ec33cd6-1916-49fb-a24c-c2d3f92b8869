package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ConfirmationDplkRevampRequest

interface IInquiryDplkRevampPresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlConfirm(urlConfirm: String)

    fun getDataConfirmation(request : ConfirmationDplkRevampRequest)

    fun getAccountDefault():String

    fun getMainBalance():String
}