package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IPresenter.base.IBaseInquiryPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.JalurRequest;

public interface IJalurPresenter <V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void getDetailJalur(JalurRequest jalurRequest);
}
