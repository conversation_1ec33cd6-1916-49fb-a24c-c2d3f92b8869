package id.co.bri.brimo.contract.IPresenter.transferrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.transferrevamp.AftConfirmationResponse

interface IKonfirmasiAftPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun getDataPayment(pin: String, refnum: String?, isFromEdit: Boolean)
    fun setUrlPayment(urlPayment: String)
}