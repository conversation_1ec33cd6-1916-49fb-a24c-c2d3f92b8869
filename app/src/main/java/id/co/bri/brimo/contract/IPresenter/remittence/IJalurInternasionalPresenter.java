package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.transferinternasional.InquiryJalurRequest;
import id.co.bri.brimo.models.apimodel.PreSumRequest;

public interface IJalurInternasionalPresenter  <V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void setUrlPreSum(String url);

    void getPreSum(PreSumRequest preSumRequest);

    void getInquiryJalur(InquiryJalurRequest jalurRequest);

    void setUrlAdditional(String url);

    void getAdditional(String refNum);
}
