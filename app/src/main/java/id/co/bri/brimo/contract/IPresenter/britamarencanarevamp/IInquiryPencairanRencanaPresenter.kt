package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dashboardrencanarevamp.KonfirmasiPencairanRencanaRequest

interface IInquiryPencairanRencanaPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrlKonfirmasi(urlKonfirmasi: String)

    fun getDataKonfirmasi(konfirmasiRequest: KonfirmasiPencairanRencanaRequest)
}