package id.co.bri.brimo.presenters.pfm;

import id.co.bri.brimo.contract.IPresenter.pfm.ITransaksiPemasukanPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.ITransaksiPemasukanView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.PFMModel;
import id.co.bri.brimo.models.apimodel.request.FastListPFMRequest;
import id.co.bri.brimo.models.apimodel.request.ListPFMRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.PFMResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TransaksiPemasukanPresenter<V extends IMvpView & ITransaksiPemasukanView> extends MvpPresenter<V> implements ITransaksiPemasukanPresenter<V> {

    private static final String TAG = "TransaksiPemasukanPresenter";

    private PFMResponse pfmResponse = new PFMResponse();
    private List<PFMModel> pfmModelList = new ArrayList<>();
    private String urlPFM;
    protected Object fastListPFMRequest = null;

    public TransaksiPemasukanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        urlPFM = url;
    }

    @Override
    public void getListPFM(boolean isFromFastMenu) {
        if (getView() != null) {
            getView().showSkeleton();
            if (isFromFastMenu) {
                fastListPFMRequest = new FastListPFMRequest(getFastMenuRequest(), Constant.PFM_INCOME);
            } else {
                fastListPFMRequest = new ListPFMRequest(Constant.PFM_INCOME);
            }

            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getCompositeDisposable()
                    .add(getApiSource().getData(urlPFM, fastListPFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    pfmResponse = response.getData(PFMResponse.class);
                                    if (pfmResponse != null) {
                                        pfmModelList.addAll(pfmResponse.getPfmList());
                                        getView().getListPFMSuccess(pfmResponse);
                                    } else {
                                        pfmModelList.clear();
                                        getView().isEmpty(pfmModelList);
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())) {
                                        getView().errorNotLogin(restResponse.getDesc());
                                    } else if (restResponse.getCode().equals("FO")) {
                                        EmptyStateResponse response = restResponse.getData(EmptyStateResponse.class);
                                        getView().onExceptionFO(response);
                                    } else {
                                        getView().onException(restResponse.getDesc());
                                    }
                                }
                            }));
        }
    }
}