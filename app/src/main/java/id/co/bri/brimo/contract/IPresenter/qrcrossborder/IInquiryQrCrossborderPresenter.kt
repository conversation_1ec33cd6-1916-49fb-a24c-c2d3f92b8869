package id.co.bri.brimo.contract.IPresenter.qrcrossborder

import id.co.bri.brimo.contract.IPresenter.base.IBaseInquiryPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView

interface IInquiryQrCrossborderPresenter<V> :
    IBaseInquiryPresenter<V> where V : IMvpView?, V : IBaseInquiryView? {

    override fun getDataConfirmation(refNum: String?, accountNum: String?, amount: String?, save: String?, fromFast: Boolean)

}
