package id.co.bri.brimo.contract.IPresenter.pengelolaankartu

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.activationdebit.response.SubmitActivationDebitResponse
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq
import id.co.bri.brimo.models.apimodel.response.ListPengelolaanKartuRes

interface IPengelolaanKartuNewPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlCardList(urlCardList: String)
    fun setUrlCardDetail(urlDetail: String)
    fun getAccountWithCardNumber()
    fun getCardDetail(detailKelolaKartuReq: DetailKelolaKartuReq)
    fun savePengelolaanKartu(pengelolaanKartuRes: ListPengelolaanKartuRes)
    fun setUrlInitActivation(urlInitActivation: String)
    fun getInitActivation()
    fun getSavedCardList(response: SubmitActivationDebitResponse)
    fun updateCardAccount(cardNumber: String)
}