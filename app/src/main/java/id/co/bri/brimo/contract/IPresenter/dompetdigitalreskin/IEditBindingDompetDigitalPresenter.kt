package id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.IEditBindingDompetDigitalView

interface IEditBindingDompetDigitalPresenter<V : IEditBindingDompetDigitalView> : IMvpPresenter<V> {
    
    /**
     * Set the URL for e-wallet unbinding API
     */
    fun setUrlEwalletUnBinding(url: String)
    
    /**
     * Unbind e-wallet by type
     */
    fun setUnbindingEwallet(type: String?)
}
