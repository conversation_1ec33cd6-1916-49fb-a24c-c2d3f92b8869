package id.co.bri.brimo.contract.IPresenter.sbnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.esbn.GetRedeemRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnDetailPortoRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnSimulasiRequest

interface ISbnDetailPortoRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setIUrlGetInquiry(urlInquiry: String)

    fun setUrlGetHistory(urlHistory: String)

    fun getInquiryPencairan()

    fun getHistoryRedeem(request: GetRedeemRequest)
}