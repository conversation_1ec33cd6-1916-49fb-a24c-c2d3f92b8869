package id.co.bri.brimo.contract.IPresenter.openaccount;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IPendingGeneralOpenAccountPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getCekStatus(String ref);

    void setUrl(String urlPayment);

}
