package id.co.bri.brimo.contract.IPresenter.regisEsbn;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.EsbnRegistrationAccountRequest;
import id.co.bri.brimo.models.apimodel.request.KonfimasiBukaValasRequest;

public interface IEsbnInquiryPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void getRegistration(EsbnRegistrationAccountRequest request);

    void setUrlRegistration(String konfirmasiUrl);
}
