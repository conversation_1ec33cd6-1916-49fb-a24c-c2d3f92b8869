package id.co.bri.brimo.contract.IPresenter.britamajunio;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.junio.InquiryJunioRequest;

public interface IFormJunioPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void setUrl(String urlInquiry);

    void getDataInquiry(InquiryJunioRequest request);

}
