package id.co.bri.brimo.contract.IPresenter.activationdebit

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.activationdebit.SubmitActivationDebitRequest

interface IActivationDebitConfirmationPresenter <V: IMvpView>: IMvpPresenter<V> {
    fun getConfirmationActivationDebit(urlConfirmation: String, request: SubmitActivationDebitRequest)
}