package id.co.bri.brimo.contract.IPresenter.qrtransfer;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IQrTransferScanPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setInquiryUrl(String urlInquiry);

    void setKonfirmasiUrl(String urlKonfirmasi);

    void setPaymentUrl(String urlPayment);

    void geDataInquiry(String qrCode);
}
