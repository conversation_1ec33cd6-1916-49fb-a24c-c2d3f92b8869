package id.co.bri.brimo.contract.IPresenter.lifestyle

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.lifestyle.LifestylePatternRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.RequestMoliga

interface IWebviewLifestylePresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlRevokeSession(urlRevoke: String)

    fun setUrlConfirmation(urlConfirmation: String)

    fun setUrlConfirmationMoliga(urlConfirmationMoliga: String)

    fun setUrlPaymentMoliga(urlPaymentMoliga: String)

    fun getRevokeSession(sessionId: String, isInquiry: Boolean)

    fun getConfirmation(confirmationRequest: LifestylePatternRequest)

    fun getConfirmationMoliga(requestMoliga: RequestMoliga)

}