package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.BranchRequest
import id.co.bri.brimo.models.apimodel.request.juniorevamp.ConfirmationRequest

interface IBritamaJunioTabunganInquiryRevPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlConfirmation(url :String)

    fun getConfirmation(response: ConfirmationRequest)

    fun setUrlPayment(urlPayment : String)

    fun getBranchCode(request: BranchRequest)

    fun setUrlPilihKode(url : String)

}