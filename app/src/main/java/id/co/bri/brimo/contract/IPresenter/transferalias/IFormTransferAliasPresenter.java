package id.co.bri.brimo.contract.IPresenter.transferalias;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryBiFastRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryRtgsRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasBriRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRequest;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;

public interface IFormTransferAliasPresenter <V extends IBaseFormView>
        extends IBaseFormPresenter<V> {

    void getDataInquiry(InquiryTransferAliasRequest inquiryTransferAliasRequest, boolean isFromFastMenu);

    void getDataInquiryBri(InquiryTransferAliasBriRequest inquiryTransferAliasBriRequest, boolean isFromFastMenu);

    void getDataInquiryRtgs(InquiryRtgsRequest inquiryRtgsRequest, boolean isFromFastMenu);

    void getDataInquiryBiFast(InquiryBiFastRequest inquiryBiFastRequest, boolean isFromFastMenu);

    void setUrlPending(String urlPending);

    void setUrlInquiryRtgs(String urlInquiryRtgs);

    void setUrlInquiryBri(String urlInquiryRtgs);

    void setUrlInquiryBiFast(String urlInquiryBiFast);

    void setUpdateItemTf(String url, SavedResponse savedResponse, int position, int type);
}
