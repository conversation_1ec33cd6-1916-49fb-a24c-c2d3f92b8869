package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.FormTopupDplkRequest
import java.util.Objects

interface IFormTopupDplkPresenter<V : IBaseFormRevampView> : IBaseFormRevampPresenter<V> {
    fun getDataInquiry(request : FormTopupDplkRequest)
    fun getClaimDplkUrl(url : String)
    fun getDetailClaimBrifine(request: DetailKlaimDplkRequest)
}