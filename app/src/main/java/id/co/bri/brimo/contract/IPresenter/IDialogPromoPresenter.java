package id.co.bri.brimo.contract.IPresenter;

import id.co.bri.brimo.contract.IView.IDialogPromoView;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.NotifikasiModel;

public interface IDialogPromoPresenter<V extends IMvpView & IDialogPromoView> extends IMvpPresenter<V> {
    void getUrl(String url);

    void getDetailPromo(String promoId, NotifikasiModel notifikasiModel);

    void getDetailPromoProduct(String promoId, String blastId,String additional);

}