package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingUserPasRequest
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingUsernameRequest

interface IOnboardingUserPassPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlUsername(url: String)

    fun sendUserCheck(onboardUsernameRequest: OnboardingUsernameRequest)

    fun setUrlUserPass(url: String)

    fun sendUserPass(onboardUserPassRequest: OnboardingUserPasRequest)

    fun getDeviceId(): String
}