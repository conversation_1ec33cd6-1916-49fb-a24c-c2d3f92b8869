package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.RegisResendReq
import id.co.bri.brimo.models.apimodel.request.RegisSendOtpReq

interface IRegistrasiOtpPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlResend(url: String)

    fun setUrlSend(url: String)

    fun sendResendOtp(regisResendReq: RegisResendReq)

    fun sendSendOtp(regisSendOtpReq: RegisSendOtpReq)
}