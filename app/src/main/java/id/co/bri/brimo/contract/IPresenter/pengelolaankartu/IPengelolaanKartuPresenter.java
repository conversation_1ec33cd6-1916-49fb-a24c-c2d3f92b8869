package id.co.bri.brimo.contract.IPresenter.pengelolaankartu;


import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;

public interface IPengelolaanKartuPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlCardList(String urlCardList);

    void setUrlCardDetail(String urlDetail);

    void getAccountWithCardNumber();

    void getCardDetail(DetailKelolaKartuReq detailKelolaKartuReq);
}
