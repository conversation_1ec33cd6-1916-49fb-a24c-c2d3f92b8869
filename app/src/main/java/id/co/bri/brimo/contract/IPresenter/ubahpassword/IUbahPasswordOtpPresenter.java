package id.co.bri.brimo.contract.IPresenter.ubahpassword;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IUbahPasswordOtpPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void getOtp(String otp);

    void sendOtp();

    void onResendOtpSubmit();

    void setUrlValidateOtp(String validateOtp);

    void setUrlValidateResendOtp(String validateResendOtp);

}
