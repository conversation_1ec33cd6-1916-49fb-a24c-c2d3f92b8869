package id.co.bri.brimo.contract.IPresenter.britamarencana;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IDetailRencanaPresenter  <V extends IMvpView>
        extends IMvpPresenter<V> {
    void setUrl(String url);

    void setUrlPencairan(String url);

    void setUrlClose(String url);

    void setUrlCertif(String url);

    void getDetailRencana(String accountNumber);

    void getFormPencairan(String accountNumber);

    void getCertif(String accountNumber);

    void getKonfirmasiTutup(String accountNumber);
}
