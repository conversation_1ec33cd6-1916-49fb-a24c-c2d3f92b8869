package id.co.bri.brimo.contract.IPresenter.splitbill

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.splitbill.GenerateBillResponse

interface ISplitBillDetailPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun fetchData(generateBillResponse: GenerateBillResponse)

    fun updateMemberPaymentStatusOne(
        memberId: Int,
        isPaidOff: Boolean,
        qrId: Int,
        mAmount: Long,
        isFromDetailMember: Boolean
    )

    fun updateMemberPaymentStatusAll()

    fun doFilterRecentMembers(searchQuery: String)

    fun setUrlMarkPayment(urlMarkPayment: String)

    fun setUrlGetDetailBills(urlDetail: String)

    fun getDataMarkPaymentStatus(isAllDone: Boolean)

    fun getDetailBills(billId: Int)

}
