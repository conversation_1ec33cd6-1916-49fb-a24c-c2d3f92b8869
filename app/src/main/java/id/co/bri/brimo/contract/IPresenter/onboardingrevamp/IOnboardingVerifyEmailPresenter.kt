package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingEmailRequest

interface IOnboardingVerifyEmailPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlResend(url: String)

    fun setUrlCheckProgress(url: String)

    fun getDeviceId(): String

    fun sendResend(resendRequest: OnboardingEmailRequest)

    fun sendCheckProgress()
}