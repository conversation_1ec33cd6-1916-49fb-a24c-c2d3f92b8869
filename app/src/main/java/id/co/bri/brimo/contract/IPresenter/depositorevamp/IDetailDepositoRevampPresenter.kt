package id.co.bri.brimo.contract.IPresenter.depositorevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.UpdatePerpanjanganDepositoRequest

interface IDetailDepositoRevampPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrlRenewalDeposito(urlDeposito: String?)
    fun getDataRenewalDeposito(account: String?)
    fun setUrlInquiryPenutupanDeposito(urlInquiryPenutupanDeposito: String?)
    fun setUrlUpdatePerpanjangan(url: String?)
    fun getInquiryPenutupanDeposito(account: String?)

    fun sendUpdatePerpanjangan(request: UpdatePerpanjanganDepositoRequest?)

    fun setUrlConfirmationPenutupanDeposito(url :String)

    fun getConfirmationPenutupanDeposito(refnum :String)

}