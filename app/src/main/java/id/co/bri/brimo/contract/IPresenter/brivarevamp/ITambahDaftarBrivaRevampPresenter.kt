package id.co.bri.brimo.contract.IPresenter.brivarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.InquiryBrivaRequest

interface ITambahDaftarBrivaRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlInquiry(urlInquiry: String)

    fun getDataInquiry(request: InquiryBrivaRequest)
}