package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.ValidateOtpPrivyReq

interface IOnboardingOtpPrivyPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlSendOtp(url: String)

    fun setUrlResendOtp(url: String)

    fun sendOtp(request: ValidateOtpPrivyReq)

    fun resendOtp()

    fun getDeviceId() : String
}