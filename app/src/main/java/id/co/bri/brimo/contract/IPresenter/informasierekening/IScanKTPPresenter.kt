package id.co.bri.brimo.contract.IPresenter.informasierekening

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.newonboardingrevamp.InitializeZolozRequest

interface IScanKTPPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlPresignUrlKtp(url: String)
    fun setUrlInitializeZoloz(url: String)
    fun setUrlCheckKtp(url: String)
    fun setUrlSendKyc(url: String)

    fun getPresignUrlKtp()
    fun getInitializeZoloz(initializeZolozRequest: InitializeZolozRequest)
    fun putUploadKtp(url: String, name: String, path: String, contentType: String)
    fun sendCheckKtp(status: String)
    fun sendKyc()
}