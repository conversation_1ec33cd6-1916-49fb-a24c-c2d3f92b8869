package id.co.bri.brimo.contract.IPresenter.pengkiniandata;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPengkinianRequest;

public interface IFormInfoPekerjaanPengkinianPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String urlKonfirm);

    void sendKonfirm(KonfirmasiPengkinianRequest request);
}
