package id.co.bri.brimo.contract.IPresenter.brivarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IInquiryBrivaPartialRevampPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDefaultSaldo()

    fun setUrlConfirm(urlConfirm: String)

    fun getDataConfirmation(
        refNum: String,
        accountNum: String,
        amount: String,
        save: String,
        note: String,
        fromFast: Boolean,
        journeyType: String
    )
}
