package id.co.bri.brimo.contract.IPresenter.splitbill

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.splitbill.ProcessSplitBillResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.SplitBillDetailResponse
import id.co.bri.brimo.models.splitbill.SplitBillAddMemberItemViewModel
import id.co.bri.brimo.models.splitbill.SplitBillEditFormItemViewModel

interface ISplitBillSharePresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun fechData(splitBillDetailResponse: SplitBillDetailResponse)

    fun setData(
        processSplitBillResponse: ProcessSplitBillResponse,
        tempBillViewModel: MutableList<SplitBillEditFormItemViewModel>
    )

    fun updateAddOrDeleteMember(members: List<SplitBillAddMemberItemViewModel>)

    fun getCurrentMembers()

    fun onResetAllSharedBillMember()

    fun onShareBillEvenly()

    fun initShareProductMembers(product: SplitBillEditFormItemViewModel)

    fun onProductSharedToMembers(product: SplitBillEditFormItemViewModel, members: List<SplitBillAddMemberItemViewModel>)

    fun setUrlGetMember(url: String)

    fun setUrlConfirmation(urlConfirm: String)

    fun setUrlDraftShare(urlDraft: String)

    fun setUrlHistoryList(urlHistory: String)

    fun getDraftShare()

    fun onBackFromHistory()

    fun getConfirmationSplitBill()

    fun onButtonSubmitClick()

}
