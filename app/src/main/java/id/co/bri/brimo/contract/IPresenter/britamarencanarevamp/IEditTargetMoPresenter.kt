package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.AturKonfirmasiRequest

interface IEditTargetMoPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlKonfirmasi(url : String)

    fun getDataConfirmation(request: AturKonfirmasiRequest)
}