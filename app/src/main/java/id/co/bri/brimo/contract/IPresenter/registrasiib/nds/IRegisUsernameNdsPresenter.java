package id.co.bri.brimo.contract.IPresenter.registrasiib.nds;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.StatLinkRequest;

public interface IRegisUsernameNdsPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void checkDevice();

    void generateDeviceId();

    void setUrlStatus(String url);

    void getStatusLink(StatLinkRequest statLinkRequest);
}
