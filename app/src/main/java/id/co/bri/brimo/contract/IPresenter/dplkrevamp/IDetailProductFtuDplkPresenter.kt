package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryFormDataFtuDplkRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailPerformanceTabDplkResponse

interface IDetailProductFtuDplkPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlDataInquiryFormData(urlInquiryFormData: String)

    fun setUrlPersonalDataRegistration(url: String)

    fun getDataInquiryFormDataFtuDplk(request: InquiryFormDataFtuDplkRequest)

    fun getPersonalDataRegistration()

    fun saveDataGraphicDplk(response: DetailPerformanceTabDplkResponse)

    fun getDataGraphicDplk(): DetailPerformanceTabDplkResponse

    fun deleteDataGraphicDplk()
}