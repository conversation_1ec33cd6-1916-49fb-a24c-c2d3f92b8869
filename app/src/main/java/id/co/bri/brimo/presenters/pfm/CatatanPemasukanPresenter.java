package id.co.bri.brimo.presenters.pfm;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.contract.IPresenter.pfm.ICatatanPemasukanPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.ICatatanPemasukanView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.daomodel.AnggaranCategory;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableMaybeObserver;

public class CatatanPemasukanPresenter<V extends IMvpView & ICatatanPemasukanView> extends MvpPresenter<V> implements ICatatanPemasukanPresenter<V> {

    private long totalPemasukan = 0;
    private static final String TAG = "CatatanPemasukanPresenter";

    private DisposableMaybeObserver disposableTransaksi = null;
    private DisposableMaybeObserver disposableMaybeObserver = null;
    private Disposable disposableAnggaran = null;
    private List<AnggaranCategory> listAnggaran = new ArrayList<>();


    public CatatanPemasukanPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void start() {
        super.start();
    }
}

