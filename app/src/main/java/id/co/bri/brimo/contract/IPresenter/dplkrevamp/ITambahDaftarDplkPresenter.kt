package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.InquiryBrivaRequest
import id.co.bri.brimo.models.apimodel.request.InquiryDplkRequest

interface ITambahDaftarDplkPresenter <V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlInquiry(urlInquiry: String)

    fun getDataInquiry(request: InquiryDplkRequest)
}