package id.co.bri.brimo.presenters.rdn

import id.co.bri.brimo.contract.IPresenter.rdn.ITopupRdnPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.rdn.ITopupRdnView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig

import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.rdn.InquiryRdnResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class TopupRdnPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        categoryPfmSource: CategoryPfmSource,
        transaksiPfmSource: TransaksiPfmSource,
        anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        ITopupRdnPresenter<V> where V : IMvpView, V : ITopupRdnView {

    private var urlInquiryRdn: String? = null
    override fun setUrlInquiryRdn(url: String?) {
        urlInquiryRdn = url
    }

    override fun getTopUpRdn(request: InquiryRdnRequest?) {
        if (isViewAttached) {
            val seqNum = brImoPrefRepository.seqNumber
            view.showProgress()
            compositeDisposable.add(
                    apiSource.getData(urlInquiryRdn, request, seqNum)
                            .subscribeOn(schedulerProvider.single())
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView().hideProgress()
                                    getView().onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    val responResend = response.getData(InquiryRdnResponse::class.java)
                                    getView().onSuccessInquiry(responResend)
                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) getView().onSessionEnd(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value, ignoreCase = true)){
                                        getView().onException12(restResponse.desc)
                                            }
                                    else getView().onException(restResponse.desc)
                                }
                            })
            )
        }
    }

}