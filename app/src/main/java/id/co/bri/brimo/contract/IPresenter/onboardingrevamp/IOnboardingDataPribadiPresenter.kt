package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingDataPribadiReq

interface IOnboardingDataPribadiPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrl(url: String)

    fun getDeviceId(): String

    fun sendSelfData(onboardingDataPribadiReq: OnboardingDataPribadiReq)
}