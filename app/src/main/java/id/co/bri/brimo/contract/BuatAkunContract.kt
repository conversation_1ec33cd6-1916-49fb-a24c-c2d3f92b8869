package id.co.bri.brimo.contract

import id.co.bri.brimo.models.inputlayout.error.InputError

class BuatAkunContract {

    interface View : BaseContract.View<Presenter>{
        /**
         * If the edittexts inputs are correct after button clicked
         */
        fun onCorrectInput()

        /**
         * If the edittexts input are wrong after button clicked
         */
        fun onWrongInput(inputError: InputError)
    }

    interface Presenter : BaseContract.Presenter {

        /**
         * Check the input credentials in presenter
         */
        fun onCheckDataInput(stringList: ArrayList<String>)
    }

}