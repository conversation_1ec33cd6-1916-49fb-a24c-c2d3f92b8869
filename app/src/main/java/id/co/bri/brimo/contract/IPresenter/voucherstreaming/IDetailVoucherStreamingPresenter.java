package id.co.bri.brimo.contract.IPresenter.voucherstreaming;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.DetailVoucherStreamingRequest;

public interface IDetailVoucherStreamingPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void sendInquiry(String streamingId, String userId, String zoneId, String productCode, boolean isFromFastMenu);

    void setInquiryUrl(String urlInquiry);

    void setKonfirmasiUrl(String urlKonfirmasi);

    void setPaymentUrl(String urlPayment);

    void setDetailUrl(String detailUrl);

    void getDetailVoucherStreaming(DetailVoucherStreamingRequest detailVoucherStreamingRequest);
}
