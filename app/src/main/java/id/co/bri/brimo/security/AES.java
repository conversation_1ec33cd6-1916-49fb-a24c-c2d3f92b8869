package id.co.bri.brimo.security;


import android.app.Application;
import android.os.Build;
import android.util.Log;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.WordEncapsulator;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public final class AES extends Application {

    private static final String TAG = "Ayowes";
    private static final int TAG_LENGTH_BIT = 128;
    private static final int AES_KEY_BIT = 256;
    private static SecretKeySpec secretKey;
    private static IvParameterSpec iv;

    private AES() {
        WordEncapsulator wordEncapsulator = new WordEncapsulator();
        String myKey = wordEncapsulator.getMyKey();

        secretKey = new SecretKeySpec(myKey.getBytes(StandardCharsets.UTF_8), Constant.AES);

        String ivstr = null;
        try {
            ivstr = MyCryptStatic.decryptAsBase64(AppConfig.IV_STRING);

            if (ivstr != null) {
                byte[] ivByte = ivstr.getBytes(StandardCharsets.UTF_8);
                iv = new IvParameterSpec(ivByte);
            }
        } catch (IOException e) {

        }

    }

    public static String encrypt(String plainText, String randomKey) {
        secretKey = new SecretKeySpec(AppConfig.getSecret().getBytes(StandardCharsets.UTF_8), Constant.AES);

        String padd = GeneralHelper.padding(randomKey, "F", 16, false);
        byte[] ivByte = padd.getBytes(StandardCharsets.UTF_8);
        iv = new IvParameterSpec(ivByte);
        String result;

        try {
            Cipher cipher = Cipher.getInstance(GeneralHelper.getString(R.string.cip_transformer));
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
            result = (Base64.encodeBytes(cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8))));
        } catch (Exception e) {
            result = "failed encrypt local";
        }

        return result;
    }

    public static String decrypt(String encryptedText) {
        WordEncapsulator wordEncapsulator = new WordEncapsulator();
        String myKey = wordEncapsulator.getMyKey();
        secretKey = new SecretKeySpec(myKey.getBytes(StandardCharsets.UTF_8), Constant.AES);

        String ivstr = null;
        try {
            ivstr = MyCryptStatic.decryptAsBase64(AppConfig.IV_STRING);

            if (ivstr != null) {
                byte[] ivByte = ivstr.getBytes(StandardCharsets.UTF_8);
                iv = new IvParameterSpec(ivByte);
            }
        } catch (IOException e) {

        }

        String result;
        try {
            Cipher cipher = Cipher.getInstance(GeneralHelper.getString(R.string.cip_transformer));
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
            result = new String(cipher.doFinal(Base64.decode64(encryptedText)));
        } catch (Exception e) {
            result = "failed decrypt local";
        }
        return result;
    }

    private static SecretKey generateGcmSecret() {
        SecretKey secretKey = null;
        byte[] key = new byte[AES_KEY_BIT / 8];

        try {
            key = AppConfig.getSecret().getBytes(StandardCharsets.UTF_8);
            secretKey = new SecretKeySpec(key, 0, key.length, "AES");
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "encryptGcm: ", e);
            }
        }

        return secretKey;
    }

    public static String encryptGcm(String plain, String iv) {
        String textChiper = "";

        try {
            Cipher cipher = Cipher.getInstance(AppConfig.ENCRYPT_ALGO);
            cipher.init(Cipher.ENCRYPT_MODE, generateGcmSecret(), new GCMParameterSpec(TAG_LENGTH_BIT, iv.getBytes(StandardCharsets.UTF_8)));
            byte[] encryptedText = cipher.doFinal(plain.getBytes(StandardCharsets.UTF_8));
            // string representation, base64, send this string to other for decryption.
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                textChiper = java.util.Base64.getEncoder().encodeToString(encryptedText);
            } else {
                textChiper = id.co.bri.brimo.security.Base64.encodeBytes(encryptedText);
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "encryptGcm: ", e);
            }
        }

        return textChiper;
    }

    public static String decryptGcm(String chip, String iv) {
        Cipher cipher = null;
        byte[] chipByte = null;
        byte[] plainText = new byte[0];

        try {
            cipher = Cipher.getInstance(AppConfig.ENCRYPT_ALGO);
            if (cipher != null) {
                cipher.init(Cipher.DECRYPT_MODE, generateGcmSecret(), new GCMParameterSpec(TAG_LENGTH_BIT, iv.getBytes(StandardCharsets.UTF_8)));
            }
        } catch (InvalidAlgorithmParameterException | InvalidKeyException |
                 NoSuchAlgorithmException | NoSuchPaddingException ignored) {

        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                chipByte = java.util.Base64.getDecoder().decode(chip);
            } else {
                try {
                    chipByte = Base64.decode64(chip);
                } catch (IOException e) {
                    // do nothing
                }
            }

            if (cipher != null) {
                plainText = cipher.doFinal(chipByte);
            }
        } catch (BadPaddingException | IllegalBlockSizeException e) {
            // do nothing
        }

        return new String(plainText, StandardCharsets.UTF_8);
    }

    public static String AESEncrypt(String plaintext, String refNum, String key) throws Exception {
        byte[] bytePlain = plaintext.getBytes(StandardCharsets.UTF_8);
        byte[] nonce = generateNonce(refNum);
        byte[] byteKey = key.getBytes(StandardCharsets.UTF_8);
        SecretKey secretKey = new SecretKeySpec(byteKey, "AES");

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, nonce);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmParameterSpec);

        byte[] ciphertext = cipher.doFinal(bytePlain);

        return bytesToHex(ciphertext);
    }

    public static byte[] generateNonce(String refnum) {
        return strPadRight(refnum, 16, "F").getBytes(StandardCharsets.UTF_8);
    }

    public static String strPadRight(String input, int padLength, String padString) {
        StringBuilder output = new StringBuilder(padString);

        while (output.length() < padLength) {
            output.append(output);
        }

        if (input.length() >= padLength) {
            return input;
        }

        return input + output.substring(input.length(), padLength);
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }
}