package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.SubmitAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.InquiryAutoPaymentResponse

interface IInquiryAutoPaymentPresenter<V : IMvpView?> : IMvpPresenter<V> {


    fun setUrlSubmitAutoPayment(urlSubmitAutopayment : String)


    fun submitAutoPayment(request: SubmitAutoPaymentRequest)


}