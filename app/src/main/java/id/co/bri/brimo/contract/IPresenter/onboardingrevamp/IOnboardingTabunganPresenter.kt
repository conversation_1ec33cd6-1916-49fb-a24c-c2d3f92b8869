package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.SelectProductRequest

interface IOnboardingTabunganPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlProduct(url: String)

    fun getDeviceId()

    fun sendSelectProduct(selectProductRequest: SelectProductRequest)
}