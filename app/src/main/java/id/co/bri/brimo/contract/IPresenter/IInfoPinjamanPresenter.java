package id.co.bri.brimo.contract.IPresenter;


import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest;

public interface IInfoPinjamanPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getAccountWithLoan();

    void setUrl(String url);

    void setUrlWebView(String urlWebView);

    void getWebView(PartnerIdRequest partnerIdRequest);
}

