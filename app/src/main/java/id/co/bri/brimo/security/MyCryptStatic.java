package id.co.bri.brimo.security;

import android.app.Application;
import android.util.Log;

import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

import java.io.IOException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import static id.co.bri.brimo.domain.config.AppConfig.ALGO;
import static id.co.bri.brimo.domain.config.AppConfig.CI_ALGORITHM;
import static id.co.bri.brimo.domain.config.AppConfig.MESSAGEDIGEST_ALGORITHM;
import static id.co.bri.brimo.domain.config.AppConfig.STRING_PRASE;

/*
 * Class MyCrypt.java
 * Digunakan untuk Simetric Encryption SSL_PINNING_URL dan SharedPreference
 *
 * */
public class MyCryptStatic extends Application {
    private static final String TAG = "MyCryptStatic";

    private static SecretKey kunciRhs;
    private static IvParameterSpec ivParameterSpec;

    // Replace me with a 16-byte key, share between Java and C#
    protected final static byte[] rawSecretKey = {
            Constant.KeyParser.rawByte, //1
            Constant.KeyParser.rawByte, //2
            Constant.KeyParser.rawByte, //3
            Constant.KeyParser.rawByte, //4
            Constant.KeyParser.rawByte, //5
            Constant.KeyParser.rawByte, //6
            Constant.KeyParser.rawByte, //7
            Constant.KeyParser.rawByte, //8
            Constant.KeyParser.rawByte, //9
            Constant.KeyParser.rawByte, //10
            Constant.KeyParser.rawByte, //11
            Constant.KeyParser.rawByte, //12
            Constant.KeyParser.rawByte, //13
            Constant.KeyParser.rawByte, //14
            Constant.KeyParser.rawByte, //15
            Constant.KeyParser.rawByte //16
    };


    public static String encryptAsBase64(String clearData1) {
        byte[] kunci = encodeDigest(STRING_PRASE);

        kunciRhs = new SecretKeySpec(kunci, CI_ALGORITHM);
        ivParameterSpec = new IvParameterSpec(rawSecretKey);
        byte[] clearData = clearData1.getBytes();
        byte[] encryptedData = encrypt(clearData);
        return Base64.encodeBytes(encryptedData);
    }

    public synchronized static String decryptAsBase64(String encrypted) throws IOException {
        if (encrypted == null || encrypted.isEmpty()) return Constant.EMPTY;

        String clearData = null;
        byte[] kunci = encodeDigest(STRING_PRASE);

        kunciRhs = new SecretKeySpec(kunci, CI_ALGORITHM);
        ivParameterSpec = new IvParameterSpec(rawSecretKey);
        clearData = decrypt(encrypted);

        return clearData;
    }


    private static byte[] encrypt(byte[] clearData) {
        byte[] encryptedData = null;

        try {
            Cipher ayowesData = Cipher.getInstance(ALGO);
            ayowesData.init(Cipher.ENCRYPT_MODE, kunciRhs, ivParameterSpec);

            try {
                encryptedData = ayowesData.doFinal(clearData);
            } catch (IllegalBlockSizeException e) {
                return null;
            } catch (BadPaddingException e) {
                return null;
            }
        } catch (InvalidKeyException e) {
            //Log.e(TAG, "Invalid key", e);
            return null;
        } catch (InvalidAlgorithmParameterException e) {
            //Log.e(TAG, "Invalid algorithm " + CIPHER_ALGORITHM, e);
            return null;
        } catch (NoSuchPaddingException e) {
            if(!GeneralHelper.isProd()) {
               Log.e(TAG, "NoSuchPaddingException:",e);
            }
        } catch (NoSuchAlgorithmException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, "NoSuchAlgorithmException:",e);
            }
        }

        return encryptedData;
    }

    public static String decrypt(String encryptedData) {
        String result = null;

        try {
            IvParameterSpec ivParms = new IvParameterSpec(rawSecretKey);
            Cipher ayowesData = Cipher.getInstance(ALGO);
            ayowesData.init(Cipher.DECRYPT_MODE, kunciRhs, ivParms);
            result = new String(ayowesData.doFinal(Base64.decode64(encryptedData)));
        } catch (InvalidKeyException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, "Invalid key", e);
            }
            return null;
        } catch (InvalidAlgorithmParameterException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, "Invalid algorithm ", e);
            }
            return null;
        } catch (NoSuchPaddingException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, e.getMessage());
            }

        } catch (NoSuchAlgorithmException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, e.getMessage());
            }
        } catch (BadPaddingException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, e.getMessage());
            }
        } catch (IllegalBlockSizeException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, e.getMessage());
            }
        } catch (IOException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, e.getMessage());
            }
        }

        return result;
    }

    private static byte[] encodeDigest(String text) {
        MessageDigest digest;
        try {
            digest = MessageDigest.getInstance(MESSAGEDIGEST_ALGORITHM);
            return digest.digest(text.getBytes());
        } catch (NoSuchAlgorithmException e) {
            if(!GeneralHelper.isProd()) {
                Log.e(TAG, "No such algorithm " + MESSAGEDIGEST_ALGORITHM, e);
            }
        }

        return null;
    }
}
