package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import android.content.Context
import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.AccountRequest
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.ChangeDetailPlanRequest

interface IDetailRencanaRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlEditTarget(url : String)

    fun getDataEditTarget(request : AccountRequest)

    fun setUrlCertif(urlCertif : String)

    fun getCertif(accountNumber: String?)

    fun setUrlClosePlan(url: String)

    fun getClosePlan(context: Context, changeDetailPlanRequest: ChangeDetailPlanRequest)
}