package id.co.bri.brimo.contract.IPresenter.transferVallas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.models.apimodel.request.InquiryVallasRequest;
import id.co.bri.brimo.models.apimodel.response.DataTransferResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferVallasResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public interface IFormTransferVallasPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void getData();

    void getInquiry(InquiryVallasRequest request);

    void setFormUrl(String urlConfirmation);

    void setInquiryUrl(String inquiryUrl);

    void setKonfirmasiUrl(String konfirmasiUrl);

    void setPaymentUrl(String paymentUrl);

}
