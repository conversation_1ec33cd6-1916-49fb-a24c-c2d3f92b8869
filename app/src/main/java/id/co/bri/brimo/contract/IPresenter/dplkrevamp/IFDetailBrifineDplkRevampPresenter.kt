package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.RiwayatSetoranDplkRequest

interface IFDetailBrifineDplkRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlInquiryAutoPayment(urlAddAutoPayment: String)

    fun setUrlRiwayatSetoranBrifine(urlRiwayatSetoranBrifine: String)

    fun getInquiryAutoPayment(request: InquiryAutoPaymentRequest)

    fun getRiwayatSetoranBrifine(request: RiwayatSetoranDplkRequest)

}