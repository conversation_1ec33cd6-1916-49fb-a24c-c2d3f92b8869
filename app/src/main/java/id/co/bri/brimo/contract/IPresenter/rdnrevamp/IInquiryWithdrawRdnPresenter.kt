package id.co.bri.brimo.contract.IPresenter.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.rdn.RdnConfirmationWithdrawRequest

interface IInquiryWithdrawRdnPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlConfirmationWithdraw(urlConfirmation: String)
    fun getConfirmation(request: RdnConfirmationWithdrawRequest)
}