package id.co.bri.brimo.contract.IPresenter.lupausername;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.ForgetUsernameInquReq;

public interface IFormLupaUsernamePresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void sendDataForm(ForgetUsernameInquReq request);

    void getIsNotLogin();
}
