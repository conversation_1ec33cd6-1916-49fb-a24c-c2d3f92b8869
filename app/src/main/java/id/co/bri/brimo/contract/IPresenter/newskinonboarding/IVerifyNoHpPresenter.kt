package id.co.bri.brimo.contract.IPresenter.newskinonboarding

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.Country
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.CodeValueIcon

interface IVerifyNoHpPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlVerifyNoHp(url: String)

    fun sendVerifyNoHp(countryCode: String, phoneNumber: String, method: String)
}