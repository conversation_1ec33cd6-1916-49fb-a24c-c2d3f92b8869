package id.co.bri.brimo.contract.IPresenter.qrmerchant;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IDashboardMerchantPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {
    void getStatusMerchant();

    void setUrlStatus(String url);

    void setUrlSetDefault(String url);

    void saveMid(String mId);

    void setUrlLast(String urlLast);

    void getLastTransaction();

    void setDefaultMerchant(String mId, String accNumb);
}
