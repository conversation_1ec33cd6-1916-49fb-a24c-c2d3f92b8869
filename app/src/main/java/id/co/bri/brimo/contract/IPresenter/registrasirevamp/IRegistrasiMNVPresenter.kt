package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import android.content.Context
import android.net.ConnectivityManager
import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.mnv.CheckMNVReq

interface IRegistrasiMNVPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setMaxRetry(mnvMaxRetry: Int);
    fun setUrlCheckMNV(urlCheckMNV: String)
    fun setConnectionMnv(
        ctx: Context,
        url: String
    )

    fun onCheckMNV(request: CheckMNVReq)
    fun registerNetworkCallback(context: Context)
    fun unregisterNetworkCallback()
}