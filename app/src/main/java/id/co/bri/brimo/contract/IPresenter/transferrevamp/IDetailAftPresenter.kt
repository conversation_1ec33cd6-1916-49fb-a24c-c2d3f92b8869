package id.co.bri.brimo.contract.IPresenter.transferrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IDetailAftPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun removeAft(idAft: String, pin: String)
    fun setUrlRemove(urlRemoveAft: String)
    fun setUrlInquiryEdit(urlEdit: String)
    fun inquiryEditAft(idAft: String)

    fun setUrlTimerSafetyMode(url :String)
    fun getTimerSafetyMode()
}