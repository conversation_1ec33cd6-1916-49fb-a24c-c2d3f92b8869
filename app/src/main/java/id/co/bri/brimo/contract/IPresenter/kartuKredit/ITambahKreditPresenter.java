package id.co.bri.brimo.contract.IPresenter.kartuKredit;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryKreditRequest;

public interface ITambahKreditPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataInquiry(InquiryKreditRequest request);

    void setInquiryUrl(String url);
}
