package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.FilterRiwayatSetoranDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.RiwayatMutasiDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.RiwayatSetoranDplkRequest

interface IRiwayatSetoranBrifineDplkPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlRiwayatSetoranBrifine(urlRiwayatSetoran : String)


    fun setUrlRiwayatSetoranBrifineFilter(urlFilterRiwayatSeotran : String)

    fun setUrlRiwayatMutasiBrifine(urlRiwayatMutasiBrifine: String)

    fun getRiwayatSetoranBrifine(request : RiwayatSetoranDplkRequest)

    fun getRiwayatSetoranBrifineFilter(request : FilterRiwayatSetoranDplkRequest)

    fun getRiwayatMutasiBrifine(request : RiwayatMutasiDplkRequest)

}