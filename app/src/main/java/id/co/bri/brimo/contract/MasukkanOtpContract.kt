package id.co.bri.brimo.contract

class MasukkanOtpContract {

    interface View : BaseContract.View<Presenter> {

        /**
         * Triggered if the otp pin is correct
         */
        fun onCorrectOtp()

        /**
         * If the otp pin is wrong
         */
        fun onWrongOtp()

        /**
         * If the otp pin checking is error
         */
        fun onErrorOtp()
    }

    interface Presenter : BaseContract.Presenter {
        /**
         * Check the otp input.
         *
         * @param otp: otp code
         */
        fun checkOtp(otp: String)
    }
}