package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.LocationNonCifRequest
import id.co.bri.brimo.models.apimodel.request.SelectProductRequest

interface IOnboardingPilihKantorPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlLocation(url: String)

    fun setUrlProduct(url: String)

    fun getDeviceId(): String

    fun sendLocation(locationRequest: LocationNonCifRequest)

    fun sendSelectProduct(selectProductRequest: SelectProductRequest)
}