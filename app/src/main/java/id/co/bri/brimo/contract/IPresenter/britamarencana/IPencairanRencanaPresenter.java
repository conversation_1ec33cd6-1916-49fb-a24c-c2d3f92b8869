package id.co.bri.brimo.contract.IPresenter.britamarencana;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPencairanRequest;

public interface IPencairanRencanaPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {
    void setUrl(String url);

    void getInquiryPencairan(KonfirmasiPencairanRequest konfirmasiPencairanRequest);
}
