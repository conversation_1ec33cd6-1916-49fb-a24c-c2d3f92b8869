package id.co.bri.brimo.contract.IPresenter.tariktunai;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IFormTarikPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void onClickSubmit();

    void getFormTarik();

    void getDataConfirmation(String accountNum, String merchant, String amount, String merchantCode);

    void setUrlConfirmation(String urlConfirmation);

    void setInquiryUrl(String url);

    void setFormUrl(String formUrl);



}