package id.co.bri.brimo.presenters.login;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.login.ILoginPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.login.ILoginView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.ChangeDeviceRequest;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.LoginResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.disposables.Disposable;

/**
 * Created by FNS on 20/11/2019
 */
public class LoginActivityPresenter<V extends IMvpView & ILoginView> extends MvpPresenter<V> implements ILoginPresenter<V> {

    private String urlLogin = "";
    private String urlLogout = "";
    private String urlChange = "";

    private String location = "";

    private boolean isChangeDevice;

    @Inject
    public LoginActivityPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void onLoginSubmit() {
        isChangeDevice = false;
        updateChangeDeviceFlag(isChangeDevice);
        if (getView() == null)
            return;

        String username = "";

        if (getBRImoPrefRepository().getUserAlias() != null && !getBRImoPrefRepository().getUserAlias().isEmpty()) {
            username = getBRImoPrefRepository().getUserAlias();
        } else {
            username = getView().getUsername();
        }


        if (urlLogin.isEmpty())
            return;

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        String finalUsername = username;
        Disposable disposable = getApiSource().validateUserLogin(urlLogin, finalUsername, getView().getPassword(), location, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                .subscribeOn(getSchedulerProvider().single())
                .observeOn(getSchedulerProvider().mainThread())
                .subscribeWith(new ApiObserver(getView(), seqNum) {

                    @Override
                    protected void onFailureHttp(String errorMessage) {
                        getView().hideProgress();
                        getView().onException(errorMessage);
                    }

                    @Override
                    protected void onApiCallSuccess(RestResponse response) {
                        getView().hideProgress();
                        LoginResponse loginResponse = response.getData(LoginResponse.class);

                        switch (response.getCode()) {
                            case Constant.RE_SUCCESS -> {
                                //update flag login
                                updateLoginFlag(true);
                                //update userr type
                                getBRImoPrefRepository().saveUserType(Constant.IB_TYPE);
                                // simpen user-alias
                                //on login success
                                getView().onLoginSuccess();
                            }
                            case Constant.RE_CHECKING_DEVICE -> {
                                //parsing data akan ada disini
                                getBRImoPrefRepository().saveTokenKey(loginResponse.getTokenKey());
                                getView().onSubmitSuccess(loginResponse);
                            }
                            case Constant.RE_CHANGE_DEVICE -> {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                                getView().onDeviceChanged(response.getDesc(), loginResponse);
                                isChangeDevice = true;
                                updateChangeDeviceFlag(isChangeDevice);
                            }
                            case Constant.RE_CREATE_PIN -> getView().onCreatePin();
                            case Constant.RE01 -> getView().onChangeUsername(response.getDesc());
                            case Constant.RE_CHANGE_DEVICE_MNV -> {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                                getView().onChangeMNV(response.getDesc(), loginResponse);
                            }
                        }
                        getBRImoPrefRepository().saveUserAlias(finalUsername);
                        getBRImoPrefRepository().saveNickname(loginResponse.getNickname());
                    }

                    @Override
                    protected void onApiCallError(RestResponse restResponse) {
                        getView().hideProgress();
                        if (restResponse.getCode().equals(Constant.RE_LOGIN_EXCEED)) {
                            getView().onExceptionLoginExceed(restResponse.getData(ExceptionResponse.class));
                        } else
                            getView().onException(restResponse.getDesc());
                    }
                });
        getCompositeDisposable().add(disposable);
    }

    @Override
    public void onChangeDeviceDeclined() {
        if (getView() != null) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().onLogout(urlLogout, seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            getView().onLogout(GeneralHelper.getString(R.string.declinedPindahDevice));
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                getView().onSessionEnd(restResponse.getDesc());
                            else if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_99.getValue())) {
                                getView().onException99(restResponse.getDesc());
                            } else
                                getView().onException(restResponse.getDesc());
                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void onLoginFingerprint() {
        isChangeDevice = false;
        updateChangeDeviceFlag(isChangeDevice);
        if (getView() != null) {

            getView().showProgress();
            String username = getBRImoPrefRepository().getUsername();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().validateUserLoginFingerprint(urlLogin, username, location, seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            LoginResponse loginResponse = response.getData(LoginResponse.class);
                            switch (response.getCode()) {
                                case Constant.RE_SUCCESS -> {
                                    //update flag login
                                    updateLoginFlag(true);
                                    //to dashboard
                                    getBRImoPrefRepository().saveNickname(loginResponse.getNickname());
                                    getView().onLoginSuccess();
                                }
                                case Constant.RE_CHECKING_DEVICE -> {
                                    //parsing data akan ada disini
                                    getBRImoPrefRepository().saveTokenKey(loginResponse.getTokenKey());
                                    getView().onSubmitSuccess(loginResponse);
                                }
                                case Constant.RE_CHANGE_DEVICE -> {
                                    getView().onDeviceChanged(response.getDesc(), loginResponse);
                                    isChangeDevice = true;
                                    updateChangeDeviceFlag(isChangeDevice);
                                }
                                case Constant.RE_CREATE_PIN -> getView().onCreatePin();
                                case Constant.RE01 ->
                                        getView().onChangeUsername(response.getDesc());
                                case Constant.RE_CHANGE_DEVICE_MNV ->
                                        getView().onChangeMNV(response.getDesc(), loginResponse);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException(restResponse.getDesc());
                        }
                    });
            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public void changeDevice(String refNum) {
        if (getView() != null) {

            getView().showProgress();
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            Disposable disposable = getApiSource().getData(urlChange, new ChangeDeviceRequest("", refNum), seqNum)
                    .subscribeOn(getSchedulerProvider().single())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(), seqNum) {

                        @Override
                        protected void onFailureHttp(String errorMessage) {
                            getView().hideProgress();
                            getView().onException(errorMessage);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            LoginResponse loginResponse = response.getData(LoginResponse.class);
                            if (response.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                //parsing data akan ada disini
                                getBRImoPrefRepository().saveTokenKey(loginResponse.getTokenKey());
                                getView().onChangeDevice(loginResponse.getOtpExpiredInSecond());
                                getBRImoPrefRepository().saveStatusAktivasi(false);
                            } else if (response.getCode().equals(Constant.RE_CHANGE_DEVICE_MNV)) {
                                getBRImoPrefRepository().saveUpdateTokenFirebase(false);
                                getView().onChangeMNV(response.getDesc(), loginResponse);
                            }
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            getView().onException(restResponse.getDesc());

                        }
                    });

            getCompositeDisposable().add(disposable);
        }
    }

    @Override
    public String getImageUrl() {
        return getBRImoPrefRepository().getBannerImageUrl();
    }

    @Override
    public String getUsername() {
        return getBRImoPrefRepository().getUsername();
    }

    @Override
    public String getUsernameAlias() {
        return getBRImoPrefRepository().getUserAlias();
    }

    @Override
    public void updateUserAlias(String userAlias) {
        getBRImoPrefRepository().saveUserAlias(userAlias);
    }

    @Override
    public void updateChangeDeviceFlag(boolean isChangeDevice) {
        getBRImoPrefRepository().saveChangeDeviceFlag(isChangeDevice);
    }

    @Override
    public boolean getChangeDeviceFlag() {
        return getBRImoPrefRepository().getChangeDeviceFlag();
    }

    @Override
    public void clearUserAlias() {
        getBRImoPrefRepository().deleteUserAlias();
    }

    @Override
    public void getLocation(String location) {
        this.location = location;
    }

    @Override
    public void start() {
        super.start();

    }

    @Override
    public void setUrlLogin(String urlLogin) {
        this.urlLogin = urlLogin;
    }

    @Override
    public void setUrlLogout(String urlLogout) {
        this.urlLogout = urlLogout;
    }

    @Override
    public void setUrlChange(String urlChange) {
        this.urlChange = urlChange;
    }


}