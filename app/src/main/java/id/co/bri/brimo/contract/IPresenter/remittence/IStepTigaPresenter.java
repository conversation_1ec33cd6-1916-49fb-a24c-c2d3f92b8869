package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryInternasionalRequest;

public interface IStepTigaPresenter  <V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void setUrlInquiry(String url);

    void getInquiry(InquiryInternasionalRequest internasionalRequest);
}
