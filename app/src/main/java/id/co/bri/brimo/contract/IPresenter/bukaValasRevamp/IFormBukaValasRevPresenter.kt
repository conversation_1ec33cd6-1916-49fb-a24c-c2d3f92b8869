package id.co.bri.brimo.contract.IPresenter.bukaValasRevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.bukarekening.InquiryValasRequest

interface IFormBukaValasRevPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun getInquiry(request : InquiryValasRequest)

    fun setInquiry(urlInquiry : String)
}