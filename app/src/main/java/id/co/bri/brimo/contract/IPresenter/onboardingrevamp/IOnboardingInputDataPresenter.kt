package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardSendDataReq

interface IOnboardingInputDataPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId(): String

    fun setUrlData(url: String)

    fun sendDataOnboarding(request: OnboardSendDataReq)
}