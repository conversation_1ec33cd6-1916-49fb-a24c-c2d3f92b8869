package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.emas.PaymentOnboardEmasRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse

interface IKonfrimasiTabunganRevPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun getDataPayment(
            pin: String?,
            note: String?,
            response: GeneralConfirmationResponse?,
            fromFast: Boolean)

    fun getDataPaymentEmas(request: PaymentOnboardEmasRequest)

    fun setUrlPayment(urlPayment : String)


}