package id.co.bri.brimo.contract.IPresenter.asuransiRevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.asuransirevamp.DetailAsuransiRequest

interface IListAsuransiPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlDetail(urlList : String)

    fun getDataDetail(request :DetailAsuransiRequest)
}