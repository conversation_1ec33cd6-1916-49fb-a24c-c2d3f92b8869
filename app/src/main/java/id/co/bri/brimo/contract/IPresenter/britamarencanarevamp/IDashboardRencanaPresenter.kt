package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.UbahPinRequest

interface IDashboardRencanaPresenter<V : IMvpView?> : IMvpPresenter<V> {


    fun setUrlDashboard(url : String)
    fun setUrlDashboardRencanaForced(url : String)
    fun setUrlUpdateRekening(url : String)
    fun getDataDashboardRencanaForced()
    fun getDataDashboardRencana()
    fun getUpdateRekening(request : UbahPinRequest)
}