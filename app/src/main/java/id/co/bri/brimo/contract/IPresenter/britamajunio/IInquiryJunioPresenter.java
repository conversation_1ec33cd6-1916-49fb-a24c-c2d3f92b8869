package id.co.bri.brimo.contract.IPresenter.britamajunio;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.junio.KonfirmasiJunioRequest;

public interface IInquiryJunioPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void setUrl(String url);

    void onGetConfirmation(KonfirmasiJunioRequest request);

}
