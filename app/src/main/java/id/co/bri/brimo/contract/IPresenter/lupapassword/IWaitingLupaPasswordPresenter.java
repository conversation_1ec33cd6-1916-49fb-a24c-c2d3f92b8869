package id.co.bri.brimo.contract.IPresenter.lupapassword;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ResendOtpReissueReq;

public interface IWaitingLupaPasswordPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void setUrlSend(String urlSend);

    void setUrlResend(String urlResend);

    void confirmWaiting(ValidateOtpUserPassReq validateRequest);

    void resendWaiting(ResendOtpReissueReq resendOtpRequest);

}
