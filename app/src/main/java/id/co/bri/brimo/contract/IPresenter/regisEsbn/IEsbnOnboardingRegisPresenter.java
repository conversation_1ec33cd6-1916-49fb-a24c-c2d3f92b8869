package id.co.bri.brimo.contract.IPresenter.regisEsbn;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IEsbnOnboardingRegisPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void setUrl(String url);

    void setUrlProduct(String urlProduct);

    void onGetData();

    void onGetOnProductBrief();

    void saveFirstRdn(Boolean firstRdn);

    void saveBackGeneral(int status);

}
