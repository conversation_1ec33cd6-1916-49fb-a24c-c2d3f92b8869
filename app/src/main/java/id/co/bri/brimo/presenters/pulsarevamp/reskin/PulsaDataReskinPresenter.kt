package id.co.bri.brimo.presenters.pulsarevamp.reskin

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.listrikrevamp.reskin.SavedListNs
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.reskin.IPulsaDataReskinPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.IFormListrikReskinView
import id.co.bri.brimo.contract.IView.listrikrevamp.reskin.ISavedListrikReskinView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountListEntity
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountModelNs
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.IPulsaDataReskinView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.FastPaymentOpenRevampRequest
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPulsaRequest
import id.co.bri.brimo.models.apimodel.request.PaymentRevampOpenRequest
import id.co.bri.brimo.models.apimodel.request.revampbriva.PayBrivaRevampRequest
import id.co.bri.brimo.models.apimodel.request.revamppulsa.FastConfirmationPaketCustomRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.presenters.base.BaseTransactionPresenter
import id.co.bri.brimo.presenters.listrikrevamp.reskin.FavoriteType
import id.co.bri.brimo.util.subscribeWithObserver
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.Schedulers
import kotlinx.parcelize.Parcelize
import java.util.concurrent.TimeUnit
import javax.inject.Inject

open class PulsaDataReskinPresenter<V> @Inject constructor(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    categoryPfmSource: CategoryPfmSource,
    transaksiPfmSource: TransaksiPfmSource,
    anggaranPfmSource: AnggaranPfmSource
) : BaseTransactionPresenter<V>(
    schedulerProvider,
    compositeDisposable,
    mBRImoPrefRepository,
    apiSource,
    transaksiPfmSource
), IPulsaDataReskinPresenter<V> where V : IMvpView, V: IPulsaDataReskinView {
    override fun getDataForm() {
        getView().showProgress()

        if (!isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber
        val observeUrl = if(view.fromFastmenu())
            apiSource.getData(GeneralHelper.getString(R.string.url_form_pulsa_data_v5), getFastMenuRequest(), seqNum)
        else apiSource.getDataForm(GeneralHelper.getString(R.string.url_form_pulsa_data_revamp), seqNum)

        println("PulsaDataReskinPresenter.log: ${view.fromFastmenu()}")

        observeUrl.subscribeWithObserver(
            compositeDisposable = compositeDisposable,
            schedulerProvider = schedulerProvider,
            createObserver = {
                object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        if (response.data != null){
                            val formPln = response.getData(
                                FormPulsaDataResponse::class.java
                            )
                            getView().onSuccessGetData(formPln)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()

                        if (restResponse.code == RestResponse.ResponseCodeEnum.RC_FO.value) {
                            val response = restResponse.getData(EmptyStateResponse::class.java)
                            view.onExceptionFO(response)
                        } else {
                            onApiError(restResponse)
                        }
                    }
                }
            }
        )
    }

    override fun getDataConfirmationPulsa(param: KonfirmasiPulsaRequest) {
        getView().showProgress()

        if (!isViewAttached) return

        val seqNum = brImoPrefRepository.seqNumber

        val fmParam = FastConfirmationPaketCustomRequest(
            getFastMenuRequest(),
            "",
            param.phoneNumber,
            param.providerId,
            param.saveAs,
            param.item,
            param.purchasedType,
            param.note,
            param.amount
        )

        val observeUrl = if(view.fromFastmenu())
            apiSource.getData(GeneralHelper.getString(R.string.url_confirmation_pulsa_data_fm_revamp), fmParam, seqNum)
        else apiSource.getData(GeneralHelper.getString(R.string.url_confirmation_pulsa_data_revamp), param, seqNum)

        observeUrl.subscribeWithObserver(
            compositeDisposable = compositeDisposable,
            schedulerProvider = schedulerProvider,
            createObserver = {
                object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
                        getView().hideProgress()
                        getView()?.onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideProgress()

                        if (response.data != null){
                            val data = response.getData(
                                GeneralConfirmationResponse::class.java
                            )
                            getView().onSuccessGetDataConfirmation(data)
                        }
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideProgress()

                        if (restResponse.code == RestResponse.ResponseCodeEnum.RC_FO.value) {
                            val response = restResponse.getData(EmptyStateResponse::class.java)
                            view.onExceptionFO(response)
                        } else {
                            onApiError(restResponse)
                        }
                    }
                }
            }
        )
    }

    override fun payment(param: PaymentNS) {
        getView().showProgress()
        val mUrlPay = if(view.fromFastmenu())
            GeneralHelper.getString(R.string.url_payment_pulsa_data_fm_revamp)
        else "PjBhVJD2oNh6zzQCIznEbX7TmHgDoFnuO92DaIukU4M="

        val fmParam = FastPaymentOpenRevampRequest(
            getFastMenuRequest(),
            param.referenceNumber,
            param.pin,
            param.accountNumber,
            param.pfmCategory.toString(),
        )

        if (isViewAttached && mUrlPay != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrlPay, if(view.fromFastmenu()) fmParam else param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserverKonfirmasi(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()

                            val receiptRevampResponse =
                                response.getData(ReceiptRevampResponse::class.java)

                            getView().onSuccessGetPayment(receiptRevampResponse)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    override fun addSavedList(param: SavedListNs) {

    }

    override fun updateSavedList(param: SavedListNs) {

    }

    override fun removeSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_delete_pulsa)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccess(response, FavoriteType.removeFavorite)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    override fun favoriteSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_favorite_pulsa)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccess(response, FavoriteType.favorite)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

    override fun unfavoriteSavedList(param: SavedListNs) {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_unfavorite_pulsa)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getData(mUrl, param, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                            getView().onException(errorMessage)
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            getView().onSuccess(response, FavoriteType.unfavorite)
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                            getView().onException(restResponse.desc)
                        }
                    }
                }
            )
        }
    }

//    override fun getAccountList() {
//        getView().showProgress()
//        val mUrl = GeneralHelper.getString(R.string.url_v5_account_list)
//
//        if (isViewAttached && mUrl != null) {
//            val seqNum = brImoPrefRepository.seqNumber
//            apiSource.getDataForm(mUrl, seqNum).subscribeWithObserver(
//                compositeDisposable = compositeDisposable,
//                schedulerProvider = schedulerProvider,
//                createObserver = {
//                    object: ApiObserver(view, seqNum) {
//                        override fun onFailureHttp(errorMessage: String?) {
//                            getView().hideProgress()
//                        }
//
//                        override fun onApiCallSuccess(response: RestResponse) {
//                            getView().hideProgress()
////                            val result: List<AccountModel> = restResponse.getDataList(AccountModel::class.java)
//                            val result = response.getData(AccountModelNs::class.java)
//                            getView().onSuccessAccountList(result.account.toMutableList())
//                        }
//
//                        override fun onApiCallError(restResponse: RestResponse) {
//                            getView().hideProgress()
//                        }
//                    }
//                }
//            )
//        }
//    }
}

@Parcelize
class PaymentNS (
    @SerializedName("reference_number") @Expose
    var referenceNumber: String? = null,
    @SerializedName("pin")
    @Expose
    val pin: String? = null,
    @SerializedName("account_number")
    @Expose
    val accountNumber: String? = null,
    @SerializedName("pfm_category")
    @Expose
    val pfmCategory: String? = null,
    @SerializedName("save_as")
    @Expose
    val saveAs: String="",
): Parcelable