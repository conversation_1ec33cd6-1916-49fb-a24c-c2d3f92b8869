package id.co.bri.brimo.presenters.rdn;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.rdn.IResultKtpRdnPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.rdn.IResultKtpRdnView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.rdn.RdnKtpRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnCheckpointResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class ResultKtpRdnPresenter <V extends IMvpView & IResultKtpRdnView> extends MvpPresenter<V> implements IResultKtpRdnPresenter<V> {

    private String url;

    public ResultKtpRdnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public void onSendKtp(String image,String checkpoint) {
        if (isViewAttached()){
            RdnKtpRequest rdnKtpRequest = new RdnKtpRequest(image,checkpoint);
            getView().showProgress();
            String seq = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable()
                    .add(getApiSource().getData(url,rdnKtpRequest,seq)
                    .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribeWith(new ApiObserver(getView(),seq){

                        @Override
                        protected void onFailureHttp(String type) {
                            getView().hideProgress();
                            getView().onException(type);
                        }

                        @Override
                        protected void onApiCallSuccess(RestResponse response) {
                            getView().hideProgress();
                            RdnCheckpointResponse rdnCheckpointResponse = response.getData(RdnCheckpointResponse.class);
                            getView().onSuccessSendKtp(rdnCheckpointResponse);
                        }

                        @Override
                        protected void onApiCallError(RestResponse restResponse) {
                            getView().hideProgress();
                            if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())){
                                getView().onSessionEnd(restResponse.getDesc());
                            }
                            else if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.getValue())){
                                getView().onException93(restResponse.getDesc());
                            }
                            else{
                                getView().onException(restResponse.getDesc());
                            }
                        }
                    }));
        }
    }
}