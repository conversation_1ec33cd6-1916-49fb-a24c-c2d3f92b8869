package id.co.bri.brimo.contract.IPresenter

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.daomodel.FastMenu
import id.co.bri.brimo.models.daomodel.FastMenuDefault

interface ISplashScreenPresenter<V: IMvpView> : IMvpPresenter<V> {
    fun loadDataCategory()

    fun loadDataIncome()

    fun loadDataFastMenu()

    fun loadDataFastMenuDefault()

    fun generateDeviceId()

    fun checkDevice()

    fun cekSavedFastMenu(menuAtas: List<FastMenu>, menuBawah: List<FastMenuDefault>)

    fun resetCheckPoint()

    fun subscribeTopicAll(newToken: String)

    fun getUpdateFastMenuDefault(fastMenuDefaults: List<FastMenuDefault>)

    fun updateFastMenuDefaultUpgradeVersion(
        fastMenuDefaults: List<FastMenuDefault>,
        fastMenus: List<FastMenu>
    )

    fun getDeleteFastMenuDefault(fastMenuDefaults: List<FastMenuDefault>)

    fun getDeleteFastMenu(fastMenu: List<FastMenu>)

    fun deleteFastMenuUpgradeVersion(fastMenus: List<FastMenu>)

    fun getUpdateFastMenu(fastMenus: List<FastMenu>)

    fun getDeletedSavedMenu(id: String)

    fun updateTokenFirebase()

    fun getOnboardingBrimo()

    fun setUrlOnboard(urlOnboard: String)

    fun saveIdPersistent(id: String)

    fun updateFreshInstallFlag(isFreshInstall: Boolean)

    fun deleteMenuDashboard(menuId: Int)

    fun checkAvailabilityNfc(isNfcAvailable: Boolean)
}