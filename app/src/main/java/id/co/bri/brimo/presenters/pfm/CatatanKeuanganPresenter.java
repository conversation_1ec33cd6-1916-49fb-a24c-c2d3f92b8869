package id.co.bri.brimo.presenters.pfm;

import id.co.bri.brimo.contract.IPresenter.pfm.ICatatanKeuanganPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.ICatatanKeuanganView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.DbConfig;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.MvpPresenter;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableMaybeObserver;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;

public class CatatanKeuanganPresenter<V extends IMvpView & ICatatanKeuanganView> extends MvpPresenter<V> implements ICatatanKeuanganPresenter<V> {


    private DisposableSingleObserver disposableSingleObserver = null;
    private DisposableMaybeObserver disposableMaybeObserver = null;

    @Inject
    public CatatanKeuanganPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void getTotalPemasukan(String startMonth,String endMonth, String year) {
       if(getView()!= null){

           disposableMaybeObserver = new DisposableMaybeObserver<Long>() {
               @Override
               public void onSuccess(Long id) {
                   getView().updatePemasukan(id);
               }

               @Override
               public void onError(Throwable e) {
                   getView().setEmptyPemasukan();


               }

               @Override
               public void onComplete() {

               }
           };

           getTransaksiPfmSource().getTotalAmount(DbConfig.TRX_IN, startMonth, endMonth, year)
                   .subscribeOn(Schedulers.io())
                   .observeOn(AndroidSchedulers.mainThread())
                   .subscribe(disposableMaybeObserver);

       }
    }


    @Override
    public void getTotalPengeluaran(String startMonth,String endMonth, String year) {
        if(getView()!= null){
            //create disposible
            disposableMaybeObserver = new DisposableMaybeObserver<Long>() {
                @Override
                public void onSuccess(Long total) {
                    getView().updatePengeluaran(total);
                }

                @Override
                public void onError(Throwable e) {
                    getView().setEmptyPengeluaran();

                }

                @Override
                public void onComplete() {

                }
            };

            getTransaksiPfmSource().getTotalAmount(DbConfig.TRX_OUT,startMonth, endMonth, year)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe(disposableMaybeObserver);

        }
    }

    @Override
    public void saveListPFMFirstOpen(boolean isFirstOpen) {
        getBRImoPrefRepository().saveListPFMFirstOpen(isFirstOpen);
    }

    @Override
    public void saveReportPFMFirstOpen(boolean isFirstOpen) {
        getBRImoPrefRepository().saveReportPFMFirstOpen(isFirstOpen);
    }

    @Override
    public boolean getPfmBubble() {
        return getBRImoPrefRepository().getPfmBubble();
    }

    @Override
    public void savePfmBubble(boolean isSave) {
        getBRImoPrefRepository().savePfmBubble(isSave);
    }

    @Override
    public void saveSiklusFirst(boolean isFirst) {
        getBRImoPrefRepository().saveSiklusFirst(isFirst);
    }

    @Override
    public boolean getSiklusFirstOpen() {
        return getBRImoPrefRepository().getSiklusFirstOpen();
    }

    @Override
    public boolean getReportPFMFirstOpen() {
        return getBRImoPrefRepository().getReportPFMFirstOpen();
    }

    @Override
    public boolean getListPFMFirstOpen() {
        return getBRImoPrefRepository().getListPFMFirstOpen();
    }


    @Override
    public void start() {
        super.start();
        String month = CalendarHelper.getCurrentMonth();
        String year = CalendarHelper.getCurrentYear();
        getTotalPemasukan(month, month, year);
        getTotalPengeluaran(month, month, year);
    }

    @Override
    public void stop() {
        if (disposableSingleObserver != null) {
            if (!disposableSingleObserver.isDisposed()) {
                disposableSingleObserver.dispose();
            }
        }
        super.stop();
    }
}
