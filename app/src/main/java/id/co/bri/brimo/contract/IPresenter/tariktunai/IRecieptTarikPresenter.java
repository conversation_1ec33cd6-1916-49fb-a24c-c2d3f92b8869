package id.co.bri.brimo.contract.IPresenter.tariktunai;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IRecieptTarikPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void onGetBatalTarik();

    void setFormUrl(String formUrl);

    void getInboxDetail(String refnumber);

    void setUrlDetailInbox(String urlDetailInbox);
}
