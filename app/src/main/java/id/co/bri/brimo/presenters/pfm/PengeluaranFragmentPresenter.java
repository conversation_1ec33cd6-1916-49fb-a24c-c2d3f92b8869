package id.co.bri.brimo.presenters.pfm;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.pfm.IPengeluaranPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.IPengeluaranView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.ValidationHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.PFMFormCreateResponse;
import id.co.bri.brimo.models.PFMMessageResponse;
import id.co.bri.brimo.models.apimodel.request.CreatePFMRequest;
import id.co.bri.brimo.models.apimodel.request.FastCreatePFMRequest;
import id.co.bri.brimo.models.apimodel.request.FastFormCreatePFMRequest;
import id.co.bri.brimo.models.apimodel.request.FormCreatePFMRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableSingleObserver;
import io.reactivex.schedulers.Schedulers;


public class PengeluaranFragmentPresenter<V extends IMvpView & IPengeluaranView> extends MvpPresenter<V> implements IPengeluaranPresenter<V> {

    private DisposableSingleObserver disposable = null;
    private String urlFormCreate, urlCreate;
    protected Object fastFormCreatePFMRequest = null, fastCreatePFMRequest = null;

    @Inject
    public PengeluaranFragmentPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlFormCreate(String url) {
        urlFormCreate = url;
    }

    @Override
    public void setUrlCreate(String url) {
        urlCreate = url;
    }

    @Override
    public void getFormCreatePFM(String trxType, boolean isFromFastMenu) {
        if (getView() != null) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showLoading(true);

            if (isFromFastMenu) {
                fastFormCreatePFMRequest = new FastFormCreatePFMRequest(getFastMenuRequest(), trxType);
            } else {
                fastFormCreatePFMRequest = new FormCreatePFMRequest(trxType);
            }

            getCompositeDisposable()
                    .add(getApiSource().getData(urlFormCreate, fastFormCreatePFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().showLoading(false);
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().showLoading(false);
                                    PFMFormCreateResponse data = response.getData(PFMFormCreateResponse.class);
                                    getView().getFormCreatePfmSuccess(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().showLoading(false);
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void createPFM(String categoryId, String amount, String date, String paymentType, String trxName, String trxType, boolean isFromFastMenu) {
        if (getView() != null) {
            if (validateInput()) {
                getView().showProgress();
                String seqNum = getBRImoPrefRepository().getSeqNumber();

                if (isFromFastMenu) {
                    fastCreatePFMRequest = new FastCreatePFMRequest(getFastMenuRequest(), categoryId, amount, date, paymentType, trxName, trxType);
                } else {
                    fastCreatePFMRequest = new CreatePFMRequest(categoryId, amount, date, paymentType, trxName, trxType);
                }

                getCompositeDisposable()
                        .add(getApiSource().getData(urlCreate, fastCreatePFMRequest, seqNum)
                                .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                                .subscribeOn(Schedulers.io())
                                .observeOn(AndroidSchedulers.mainThread())
                                .subscribeWith(new ApiObserver(getView(), seqNum) {

                                    @Override
                                    protected void onFailureHttp(String errorMessage) {
                                        getView().hideProgress();
                                        getView().onException(errorMessage);
                                    }

                                    @Override
                                    protected void onApiCallSuccess(RestResponse response) {
                                        getView().hideProgress();
                                        PFMMessageResponse data = response.getData(PFMMessageResponse.class);
                                        getView().createPfmSuccess(data);
                                    }

                                    @Override
                                    protected void onApiCallError(RestResponse restResponse) {
                                        getView().hideProgress();
                                        if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                            getView().onSessionEnd(restResponse.getDesc());
                                        else
                                            getView().onException(restResponse.getDesc());
                                    }
                                }));
            }
        }
    }

    protected boolean validateInput() {
        try {
            if (getView().getJumlah() < 1) {
                getView().showInputError("Nominal tidak valid");
                return false;
            }

            if (getView().getKategoriId() < 0) {
                getView().showInputError("Kategori harus dipilih");
                return false;
            }

            if (getView().getDurasi() == null) {
                getView().showInputError("Durasi belum diisi");
                return false;
            } else if (ValidationHelper.isEmpty(getView().getDurasi().getStartDateString())) {
                getView().showInputError("Durasi tidak valid");
                return false;
            }

            if (getView().getPembayaranId() < 1) {
                getView().showInputError("Tipe pembayaran harus dipilih");
                return false;
            }
        } catch (Exception e) {
            getView().showInputError(e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public void stop() {
        if (disposable != null) {
            if (!disposable.isDisposed()) {
                disposable.dispose();
            }
        }
        super.stop();
    }
}