package id.co.bri.brimo.presenters.pfm;

import id.co.bri.brimo.contract.IPresenter.pfm.ITransaksiAnggaranPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.ITransaksiAnggaranView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.PFMChartResponse;
import id.co.bri.brimo.models.apimodel.request.ChartByMonthPFMRequest;
import id.co.bri.brimo.models.apimodel.request.FastChartByMonthPFMRequest;
import id.co.bri.brimo.models.apimodel.request.FastUpdateCyclePFMRequest;
import id.co.bri.brimo.models.apimodel.request.UpdateCyclePFMRequest;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import java.util.concurrent.TimeUnit;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TransaksiAnggaranPresenter<V extends IMvpView & ITransaksiAnggaranView> extends MvpPresenter<V> implements ITransaksiAnggaranPresenter<V> {

    private static final String TAG = "TransaksiAnggaranPres";
    private String urlPFMChart, urlUpdateCycle;
    protected Object fastChartByMonthPFMRequest = null, fastUpdateCyclePFMRequest = null;

    public TransaksiAnggaranPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlPFMChart(String url) {
        urlPFMChart = url;
    }

    @Override
    public void setUrlUpdateCycle(String url) {
        urlUpdateCycle = url;
    }

    @Override
    public void savePFMCycle(String cycle, boolean isFromFastMenu) {
        if (getView() != null) {
            getView().showSkeleton(true);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            if (isFromFastMenu) {
                fastUpdateCyclePFMRequest = new FastUpdateCyclePFMRequest(getFastMenuRequest(), cycle);
            } else {
                fastUpdateCyclePFMRequest = new UpdateCyclePFMRequest(cycle);
            }

            getCompositeDisposable()
                    .add(getApiSource().getData(urlUpdateCycle, fastUpdateCyclePFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().showSkeleton(false);
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().showSkeleton(false);
                                    getView().successSetCycle();
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().showSkeleton(false);
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void getPFMChartByMonth(String month, boolean isFromFastMenu) {
        if (getView() != null) {
            getView().showSkeleton(true);
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            if (isFromFastMenu) {
                fastChartByMonthPFMRequest = new FastChartByMonthPFMRequest(getFastMenuRequest(), month);
            } else {
                fastChartByMonthPFMRequest = new ChartByMonthPFMRequest(month);
            }

            getCompositeDisposable()
                    .add(getApiSource().getData(urlPFMChart, fastChartByMonthPFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().showSkeleton(false);
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().showSkeleton(false);
                                    PFMChartResponse data = response.getData(PFMChartResponse.class);
                                    getView().successGetPFMChartByMonth(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().showSkeleton(false);
                                    onApiError(restResponse);
                                }
                            }));
        }
    }

    @Override
    public void stop() {
        super.stop();
    }

    @Override
    public void onApiError(RestResponse restResponse) {
        if (restResponse.getCode().equalsIgnoreCase(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue())) {
            getView().onSessionEnd(restResponse.getDesc());
        } else if (restResponse.getCode().equalsIgnoreCase("FO")) {
            EmptyStateResponse response = restResponse.getData(EmptyStateResponse.class);
            getView().onExceptionFO(response);
        } else
            super.onApiError(restResponse);
    }
}