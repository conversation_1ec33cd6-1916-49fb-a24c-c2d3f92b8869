package id.co.bri.brimo.contract.IPresenter.saldodompetdigital;

import java.util.List;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IHubungkanDompetDigitalPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getEwalletTnc(String type);

    void setUrlEwalletTnc(String url);

    void setUrlEwalletBinding(String url);

    void bindingEwallet(String code, String countryCode);
}