package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.EditInternationalRequest;
import id.co.bri.brimo.models.apimodel.request.stepTigaRequest;

public interface IEditTfInternationalPresenter <V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void getEditData(EditInternationalRequest editInternationalRequest);
}
