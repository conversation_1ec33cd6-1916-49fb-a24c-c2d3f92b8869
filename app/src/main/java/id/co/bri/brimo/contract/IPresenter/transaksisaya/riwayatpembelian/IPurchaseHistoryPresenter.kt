package id.co.bri.brimo.contract.IPresenter.transaksisaya.riwayatpembelian

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.request.transaksisaya.riwayatpembelian.PurchaseHistoryRequest
import id.co.bri.brimo.models.apimodel.request.transaksisaya.riwayatpembelian.SearchFilterRequest
import id.co.bri.brimo.models.apimodel.response.CityFormResponse

interface IPurchaseHistoryPresenter<V: IMvpView?>: IMvpPresenter<V> {
    fun setUrlPurchaseHistory(url: String)

    fun getPurchaseHistoryResponse(request: PurchaseHistoryRequest, isRefresh: Boolean): Boolean

    fun setUrlFilterBase(url: String)

    fun getFilterBase()

    fun setUrlSearchFilter(url: String)

    fun getPurchaseHistorySearchFilter(request: SearchFilterRequest, isRefresh: Boolean): Boolean

    fun setUrlInboxDetail(url: String)

    fun getInboxDetail(refNum: String)

    fun setTrxType(trxType: String)

    fun setUrlWebViewTugu(urlTugu: String)

    fun getWebViewTugu(partnerIdRequest: PartnerIdRequest?)

    fun setUrlFormBus(urlFormBus: String)

    fun getFormBus()
}