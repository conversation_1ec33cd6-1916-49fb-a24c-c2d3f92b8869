package id.co.bri.brimo.contract.IPresenter.transaksisaya.belumdibayar

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.lifestyle.LifestylePatternRequest
import id.co.bri.brimo.models.apimodel.request.lifestyle.RequestMoliga
import id.co.bri.brimo.models.apimodel.request.transaksisaya.belumdibayar.SearchBelumDibayarRequest
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.NotifikasiMokirimRequest

interface IPendingTransactionPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlPendingTransaction(url: String)

    fun setUrlPendingTransactionSearch(url: String)

    fun setUrlInquiryTravel(url: String)

    fun setUrlConfirmationKcic(url: String)

    fun setUrlPaymentKcic(url: String)

    fun setUrlInquiryBusShuttle(url: String)

    fun setUrlConfirmationFlight(url: String)

    fun setUrlPaymentFlight(url: String)

    fun setUrlConfirmationMobelanja(url: String)

    fun setUrlPaymentMobelanja(url: String)

    fun setUrlConfirmationMoliga(url: String)

    fun setUrlPaymentMoliga(url: String)

    fun setUrlConfirmationMokirim(urlMokirim: String)

    fun setUrlPaymentMokirim(urlPayment: String)

    fun setUrlConfirmationPattern(url: String)

    fun getPendingTransactionResponse(search: Boolean)

    fun getPendingTransactionSearch(searchKey: SearchBelumDibayarRequest, search: Boolean): Boolean

    fun getInquiryTravelKai(bookingId: String, bookingRefnum: String)

    fun getConfirmationKcic(
        brivaNumber: String?,
        corpCode: String?,
        quantityTicket: String?,
        expirateDate: String?,
        arrivalDate: String?,
        invoiceNumber: String?
    )

    fun getInquiryTravelBusShuttle(bookingRefnum: String)

    fun getConfirmationTravelFlight(bookingId: String)

    fun getConfirmationMobelanja(orderNumber: String)

    fun getConfirmationMoliga(requestMoliga: RequestMoliga)

    fun getConfirmationMokirim(notifikasiMokirimRequest: NotifikasiMokirimRequest)


    fun getConfirmationPattern(confirmationLifestyleRequest: LifestylePatternRequest)
}