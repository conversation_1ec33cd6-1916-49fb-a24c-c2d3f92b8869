package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.RegisSendDataRequest

interface IRegistrasiInputDataPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId()

    fun setUrlData(urlData: String)

    fun sendDataRegis(regisSendDataRequest: RegisSendDataRequest)
}