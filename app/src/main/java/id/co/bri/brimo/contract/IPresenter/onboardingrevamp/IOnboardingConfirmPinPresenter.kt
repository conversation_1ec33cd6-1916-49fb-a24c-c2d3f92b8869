package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.PinRequest

interface IOnboardingConfirmPinPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlConfirmPin(url: String)
    fun sendConfirmPin(pinRequest: PinRequest)
    fun getDeviceId() : String
}