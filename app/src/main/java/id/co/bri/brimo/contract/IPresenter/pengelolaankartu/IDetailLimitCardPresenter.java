package id.co.bri.brimo.contract.IPresenter.pengelolaankartu;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IDetailLimitCardPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlDetails(String url);

    void getDetailsLimit(String productId, String accountNumber);

    void setUrlSubmitLimit(String url);

    void submitLimit(String pin, String productId, String limitCustom, String accountNumber);
}
