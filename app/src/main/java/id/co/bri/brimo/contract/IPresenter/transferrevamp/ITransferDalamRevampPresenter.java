package id.co.bri.brimo.contract.IPresenter.transferrevamp;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;

public interface ITransferDalamRevampPresenter <V extends IMvpView> extends IMvpPresenter<V> {
    void getDataForm();
    void getDataFormFastMenu();
    void cekFirstTimeVisit();
    void setUrlForm(String setUrlForm);
    void setUpdateItemTf(String url, SavedResponse savedResponse, int position, int type);
    void getDataInquiry(String bankCode, String accountStr, boolean isFromFastMenu);
    void setUrlInquiry(String urlInquiry);
    void checkAccessTokenC2();
    void saveStatusFirstTimeVisit(Boolean isFirstTime);
}
