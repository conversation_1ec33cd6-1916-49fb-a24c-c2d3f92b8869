package id.co.bri.brimo.contract.IPresenter.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.informasirekening.SubmitDataRequest

interface IVerifyOCRPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlSubmitData(urlData: String)

    fun sendSubmitData(submitDataRequest: SubmitDataRequest)
}