package id.co.bri.brimo.contract.IPresenter.dompetdigitalreskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.daomodel.Transaksi

interface IConfirmationDompetDigitalReskinPresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun getDataPayment(
        pin: String,
        generalConfirmationResponse: GeneralConfirmationResponse,
        fromFast: Boolean,
        isUsingC2: Boolean
    )

    fun onSaveTransaksiPfm(transaksi: Transaksi?)

    fun generateTransaksiModel(
        kategoriId: Int,
        amount: Long,
        referenceNumber: String?,
        billingName: String?
    ): Transaksi?

    fun isGeneral(general: Boolean)

    fun setUrlConfirm(urlConfirm: String)

    fun setUrlPayment(urlPayment: String)

    fun getDataConfirmation(refNum: String, accountNum: String, amount: String, save: String, note : String, fromFast: Boolean)
}