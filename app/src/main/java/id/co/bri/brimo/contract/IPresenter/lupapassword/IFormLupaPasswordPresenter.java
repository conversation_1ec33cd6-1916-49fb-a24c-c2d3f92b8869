package id.co.bri.brimo.contract.IPresenter.lupapassword;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.ForgetPassInquReq;

public interface IFormLupaPasswordPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void onSendDataInquiry(ForgetPassInquReq request);

    void getIsNotLogin();
}
