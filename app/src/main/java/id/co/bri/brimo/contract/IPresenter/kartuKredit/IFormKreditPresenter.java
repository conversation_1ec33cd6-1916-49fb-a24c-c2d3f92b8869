package id.co.bri.brimo.contract.IPresenter.kartuKredit;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryKreditRequest;

public interface IFormKreditPresenter <V extends IBaseFormView>
        extends IBaseFormPresenter<V> {

    void getDataInquiry(InquiryKreditRequest request);

}
