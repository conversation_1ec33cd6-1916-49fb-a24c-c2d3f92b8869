package id.co.bri.brimo.presenters.pfm;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.pfm.ICatatanKeuanganEditPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.ICatatanKeuanganEditView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.PFMFormUpdateResponse;
import id.co.bri.brimo.models.PFMMessageResponse;
import id.co.bri.brimo.models.apimodel.request.FastFormUpdatePFMRequest;
import id.co.bri.brimo.models.apimodel.request.FastUpdatePFMRequest;
import id.co.bri.brimo.models.apimodel.request.FormUpdatePFMRequest;
import id.co.bri.brimo.models.apimodel.request.UpdatePFMRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by user on 08/01/2021
 */
public class CatatanKeuanganEditPresenter<V extends IMvpView & ICatatanKeuanganEditView>
        extends MvpPresenter<V> implements ICatatanKeuanganEditPresenter<V> {

    private String urlFormPFM, urlUpdatePFM, paymentTypeId;
    protected Object fastFormUpdatePFMRequest = null;
    protected Object fastUpdatePFMRequest = null;

    public CatatanKeuanganEditPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrlForm(String url) {
        urlFormPFM = url;
    }

    @Override
    public void setUrlUpdate(String url) {
        urlUpdatePFM = url;
    }

    @Override
    public void getFormPfm(String trxRefnum, boolean isFromFastMenu) {
        if (getView() != null) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();

            if (isFromFastMenu) {
                fastFormUpdatePFMRequest = new FastFormUpdatePFMRequest(getFastMenuRequest(), trxRefnum);
            } else {
                fastFormUpdatePFMRequest = new FormUpdatePFMRequest(trxRefnum);
            }

            getCompositeDisposable()
                    .add(getApiSource().getData(urlFormPFM, fastFormUpdatePFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    PFMFormUpdateResponse data = response.getData(PFMFormUpdateResponse.class);
                                    getView().getFormUpdatePfmSuccess(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

    @Override
    public void updatePfm(String trfNum, String categoryId, String amount, String dateCreate, String paymentType, String description, boolean isFromFastMenu) {
        if (getView() != null) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();

            if (paymentType == null) {
                paymentTypeId = "";
            } else {
                paymentTypeId = paymentType;
            }

            if (isFromFastMenu) {
                fastUpdatePFMRequest = new FastUpdatePFMRequest(getFastMenuRequest(), trfNum, categoryId, amount, dateCreate, paymentTypeId, description);
            } else {
                fastUpdatePFMRequest = new UpdatePFMRequest(trfNum, categoryId, amount, dateCreate, paymentTypeId, description);
            }

            getCompositeDisposable()
                    .add(getApiSource().getData(urlUpdatePFM, fastUpdatePFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    PFMMessageResponse data = response.getData(PFMMessageResponse.class);
                                    getView().updatePfmSuccess(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }

}