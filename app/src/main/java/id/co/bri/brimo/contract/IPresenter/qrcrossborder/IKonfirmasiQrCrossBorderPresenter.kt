package id.co.bri.brimo.contract.IPresenter.qrcrossborder

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.qr.KonfirmasiQrCrossBorderResponse
import id.co.bri.brimo.models.daomodel.Transaksi

interface IKonfirmasiQrCrossborderPresenter<V> :
    IMvpPresenter<V> where V : IMvpView? {

    fun getDataPayment(pin: String, note: String, response: KonfirmasiQrCrossBorderResponse?, fromFast: Boolean)

    fun setUrlPayment(urlPayment: String?)
    fun generateTransaksiModel(
        kategoriId: Int,
        amount: Long,
        referenceNumber: String?,
        billingName: String?
    ): Transaksi?

    fun setIdPayment(idPayment: Long)

    fun onSaveTransaksiPfm(transaksi: Transaksi?)
}