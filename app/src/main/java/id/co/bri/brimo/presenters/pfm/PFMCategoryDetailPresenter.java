package id.co.bri.brimo.presenters.pfm;


import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.pfm.IPFMCategoryDetailPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.IPFMCategoryDetailView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.PFMCategoryResponse;
import id.co.bri.brimo.models.apimodel.request.FastListTransactionCategoryPFMRequest;
import id.co.bri.brimo.models.apimodel.request.ListTransactionCategoryPFMRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class PFMCategoryDetailPresenter<V extends IMvpView & IPFMCategoryDetailView> extends MvpPresenter<V> implements IPFMCategoryDetailPresenter<V> {

    private String urlPFM;
    protected Object fastListCategoryPFMRequest = null;

    public PFMCategoryDetailPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        urlPFM = url;
    }

    @Override
    public void getListCategoryPfm(String categoryId, String month, boolean isFromFastMenu) {
        if (getView() != null) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();

            if (isFromFastMenu) {
                fastListCategoryPFMRequest = new FastListTransactionCategoryPFMRequest(getFastMenuRequest(), categoryId, month);
            } else {
                fastListCategoryPFMRequest = new ListTransactionCategoryPFMRequest(categoryId, month);
            }

            getCompositeDisposable()
                    .add(getApiSource().getData(urlPFM, fastListCategoryPFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.getValue())) {
                                        PFMCategoryResponse data = response.getData(PFMCategoryResponse.class);
                                        getView().successGetCategoryPFM(data);
                                    } else {
                                        getView().setEmptyState();
                                    }
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }

    }

    @Override
    public void stop() {
        super.stop();
    }
}