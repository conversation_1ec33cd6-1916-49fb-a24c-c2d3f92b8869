package id.co.bri.brimo.contract.IPresenter.pulsarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IInquiryPulsaRevampPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun getDataConfirmationPulsaRevamp(
        referenceNumber: String,
        providerId: String,
        phoneNumber: String,
        accountNumber: String,
        saveAs: String,
        item: String,
        type: String,
        note: String,
        fromFast: Boolean
    )

    fun setUrlConfirmation(urlConfirmation: String)

    fun getCustomPaket(phoneNumber: String, operatorId: String, isFromFast: Boolean)

    fun getDataConfirmationPulsaRevampCustom(
        referenceNumber: String,
        providerId: String,
        phoneNumber: String,
        saveAs: String,
        item: String,
        type: String,
        note: String,
        fromFast: Boolean
    )

    fun setUrlIndosat(urlIndosat: String)

}