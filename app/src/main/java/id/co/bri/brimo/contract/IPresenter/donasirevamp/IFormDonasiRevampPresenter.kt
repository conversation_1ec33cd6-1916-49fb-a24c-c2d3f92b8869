package id.co.bri.brimo.contract.IPresenter.donasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.request.donasirevamp.InquiryDonasiRevampRequest

interface IFormDonasiRevampPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDataForm()
    fun setUrlForm(url: String)
    fun setUrlInquiry(url: String)
    fun getDataInquiry(inquiryRequest: InquiryDonasiRevampRequest)
}