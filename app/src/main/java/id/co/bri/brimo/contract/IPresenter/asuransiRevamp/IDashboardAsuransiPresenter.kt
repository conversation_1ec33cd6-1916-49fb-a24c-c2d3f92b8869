package id.co.bri.brimo.contract.IPresenter.asuransiRevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.asuransirevamp.DetailInsuranceRequest

interface IDashboardAsuransiPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlDashboard(url : String)

    fun getDataDashboard()

    fun setUrlList(urlList : String)

    fun getDataList()

    fun setUrlInsuranceDetail(url : String)

    fun getDataDetailInsurance(request: DetailInsuranceRequest)

    fun setUrlProduct(urlProduct : String)

    fun getProduct(patnerId : String)

    fun getProdukLama(patnerId: String?)

    fun setUrlProdukLama(urlProdukLama: String?)
}