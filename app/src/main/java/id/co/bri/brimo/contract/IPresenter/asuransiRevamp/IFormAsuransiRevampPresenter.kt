package id.co.bri.brimo.contract.IPresenter.asuransiRevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.asuransirevamp.KonfirmasiAsuransiRequest

interface IFormAsuransiRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlKonfirmasi(urlKonfirmasi : String)

    fun getDataKonfirmasi(request : KonfirmasiAsuransiRequest)

}