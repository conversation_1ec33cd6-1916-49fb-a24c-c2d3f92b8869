package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailBrifineDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.KlaimDplkRequest
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse

interface IDetailBrifineDplkRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun getDataInquiryTopUp(no_dplk: String)
    fun getDataInquiryClaimDplk(request: KlaimDplkRequest)

    fun getPusatBantuanSafety(code :String)

    fun setInquiryUrlTopUp(url: String)

    fun setUrlDetailDplk(urlDetail: String)
    fun setUrlClaimDplk(urlDetail: String)

    fun setUrlPusatBantuan(url: String)
    fun getDetailDplk(request: DetailBrifineDplkRequest)

    fun getClaimDplkUrl(url : String)
    fun getDetailClaimBrifine(request: DetailKlaimDplkRequest)




}