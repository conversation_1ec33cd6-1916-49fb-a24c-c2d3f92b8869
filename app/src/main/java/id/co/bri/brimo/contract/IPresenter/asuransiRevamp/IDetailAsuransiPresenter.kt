package id.co.bri.brimo.contract.IPresenter.asuransiRevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.asuransirevamp.DetailInsuranceRequest

interface IDetailAsuransiPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlFormAsuransi(url :String)

    fun getDataFormAsuransi(request : DetailInsuranceRequest)
}