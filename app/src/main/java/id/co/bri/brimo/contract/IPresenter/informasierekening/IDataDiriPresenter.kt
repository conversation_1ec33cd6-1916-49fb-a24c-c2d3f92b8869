package id.co.bri.brimo.contract.IPresenter.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
//import id.co.bri.brimo.models.uimodel.DataDiriFormModel

// TODO inside this class still all dummy
interface IDataDiriPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun resetError()
//    fun submitForm(dataForm: DataDiriFormModel)
}