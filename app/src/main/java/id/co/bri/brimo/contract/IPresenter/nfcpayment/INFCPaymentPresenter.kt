package id.co.bri.brimo.contract.IPresenter.nfcpayment

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface INFCPaymentPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun init(isFastMenu: Boolean)

    fun setUrlCheckPayment(urlCheckPayment: String)

    fun setUrlGeneratePayload(urlGeneratePayload: String)

    fun startCheckPaymentStatus()

    fun stopCheckPaymentStatus()

    fun getDataPayloadNfc(accountNumber: String, token: String, pin: String, nfcType: String?)

    fun getSaldoNormal(account: String)

    fun getSaldoNormalCC(account: String)
}