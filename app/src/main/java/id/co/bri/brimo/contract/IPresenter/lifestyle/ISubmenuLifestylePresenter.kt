package id.co.bri.brimo.contract.IPresenter.lifestyle

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest

interface ISubmenuLifestylePresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun onUpdateFlagNewMenu(featureCode: String)

    fun setUrlWebviewTugu(urlTugu: String)

    fun getWebViewTugu(partnerIdRequest: PartnerIdRequest?,
                       titleBar: String,
                       codeMenu: String
    )

    fun getMenuLifestyle()

}