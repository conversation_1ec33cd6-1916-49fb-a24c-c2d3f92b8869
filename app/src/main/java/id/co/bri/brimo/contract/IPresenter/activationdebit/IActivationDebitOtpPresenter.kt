package id.co.bri.brimo.contract.IPresenter.activationdebit

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.activationdebit.request.SubmitOtpActivationDebitRequest
import id.co.bri.brimo.models.activationdebit.request.ValidateActivationDebitRequest

interface IActivationDebitOtpPresenter<V: IMvpView>: IMvpPresenter<V> {
    fun getSubmitOtp(urlOtp: String, submitOtpActivationDebitRequest: SubmitOtpActivationDebitRequest)
    fun getValidateActivation(urlValidate: String, validateActivationDebitRequest: ValidateActivationDebitRequest?)
}