package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ListProductDplkRequest

interface IPickProfileRiskPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrulListProductDplk(url: String)

    fun getListProductDplk(request: ListProductDplkRequest)

}