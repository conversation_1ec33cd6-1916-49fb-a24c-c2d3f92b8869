package id.co.bri.brimo.contract.IPresenter.transferrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse

interface IListAftPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun getDataListAft()

    fun setUrlListAft(urlList: String)

    fun setUrlUpdateStatus(urlUpdateStatus: String)

    fun updateStatusAft(idAft: String, isActive: Boolean, pin: String)

    fun getDetailAft(idAft: String)

    fun setUrlDetailAft(urlDetailAft: String)
}