package id.co.bri.brimo.contract.IPresenter.pengelolaankartu

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IChangeDebitPINPresenter<V: IMvpView>: IMvpPresenter<V> {

    fun setUrlChangePin(url: String?)

    fun changePin(authPin: String?, newCardPin: String?, oldCardPin: String?, cardNumber: String?)

    fun changePinCc(authPin: String?, newCardPin: String?, oldCardPin: String?, cardToken: String?)
}