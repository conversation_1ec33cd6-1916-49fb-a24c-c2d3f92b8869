package id.co.bri.brimo.contract.IPresenter.ubahrencana

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dashboardrencanarevamp.InquiryPencairanRencanaRequest
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.ChangeDetailPlanRequest

interface IChangeDetailPlanPresenter<V: IMvpView>: IMvpPresenter<V> {

    fun setUrlChangeDetailPlan(url: String)

    fun setUrlPencairanRencana(url: String)

    fun setUrlTopUpPlan(url: String)

    fun postChangeDetailPlanRequest(changeDetailPlanRequest: ChangeDetailPlanRequest)

    fun getInquiryPencairanRencana(accountNumberRequest: InquiryPencairanRencanaRequest)

    fun postTopUpPlanRequest(changeDetailPlanRequest: ChangeDetailPlanRequest)
}