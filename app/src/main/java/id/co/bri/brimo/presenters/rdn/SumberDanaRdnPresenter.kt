package id.co.bri.brimo.presenters.rdn

import android.os.Build
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.rdn.ISumberDanaRdnPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.rdn.ISumberDanaRdnView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.converter.MapperHelper

import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.rdn.RdnInfoSaldoRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SaldoReponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnAccountResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnAccountResponse.AccountRdn
import id.co.bri.brimo.models.apimodel.response.rdn.RdnDashboardFailureResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.Observable
import io.reactivex.ObservableSource
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.functions.Function
import io.reactivex.observers.DisposableObserver
import java.util.concurrent.TimeUnit

class SumberDanaRdnPresenter<V>(
        schedulerProvider: SchedulerProvider,
        compositeDisposable: CompositeDisposable,
        mBRImoPrefRepository: BRImoPrefSource,
        apiSource: ApiSource,
        categoryPfmSource: CategoryPfmSource,
        transaksiPfmSource: TransaksiPfmSource,
        anggaranPfmSource: AnggaranPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        ISumberDanaRdnPresenter<V> where V : IMvpView, V : ISumberDanaRdnView {

    var urlList: String? = null

    var urlInfo: String? = null

    var isLoading = false

    var listRdnAccount: MutableList<AccountRdn> = mutableListOf()



    override fun getListAccount() {
        if (isLoading) return
        if (isViewAttached) {
            isLoading = true
            val seqNum: String = brImoPrefRepository.seqNumber
            compositeDisposable.add(
                    apiSource.getData(urlList, "", seqNum)
                            .subscribeOn(schedulerProvider.single())
                            .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserver(view, seqNum) {
                                override fun onFailureHttp(type: String) {
                                    getView().onException(type)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)) {
                                        val rdnAccountResponse = response.getData(RdnAccountResponse::class.java)
                                        if (listRdnAccount.isEmpty()) {
                                            listRdnAccount.addAll(rdnAccountResponse.accountRdn)
                                        } else {
                                            listRdnAccount.clear()
                                            listRdnAccount.addAll(rdnAccountResponse.accountRdn)
                                        }
                                        getView().onSuccessGetAccount(rdnAccountResponse)
                                    } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                        val rdnDashboardFailureResponse = response.getData(RdnDashboardFailureResponse::class.java)
                                        getView().onException01(rdnDashboardFailureResponse.title, rdnDashboardFailureResponse.description)
                                    } else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_02.value, ignoreCase = true)) {
                                        getView().onException02(GeneralHelper.getString(R.string.txt_halaman_gagal_memuat), response.desc)
                                    }
                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.value, ignoreCase = true)) {
                                        getView().onSessionEnd(restResponse.desc)
                                    } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_99.value, ignoreCase = true)) {
                                        getView().onException02("", restResponse.desc)
                                    } else {
                                        getView().onException(restResponse.desc)
                                    }
                                }

                                override fun onComplete() {
                                    onGetDetailAccount(listRdnAccount, false)
                                }
                            })
            )
        }
    }

    override fun setUrl(url: String?) {
        urlList = url
    }

    override fun setUrlInfoSaldo(url: String?) {
        urlInfo= url
    }

    override fun onGetDetailAccount(list: MutableList<AccountRdn>, isRefreshed: Boolean) {
        compositeDisposable.add(Observable.fromIterable<AccountRdn>(list).flatMap<AccountRdn>(object : Function<AccountRdn?, ObservableSource<AccountRdn?>?> {
            @Throws(Exception::class)
            override fun apply(t: AccountRdn): ObservableSource<AccountRdn?>? {
                return getSaldoRdn(t)
            }
        }).subscribeWith(object : DisposableObserver<AccountRdn?>() {
            override fun onNext(accountRdn: AccountRdn) {
                val postion: Int = list.indexOf(accountRdn)
                if (postion == -1) return
                list.set(postion, accountRdn)
                view.onGetSaldo(list, isRefreshed, postion)
                view.showSaldoRdn()
            }

            override fun onError(e: Throwable) {
            }

            override fun onComplete() {
                view.onGetSaldoComplete()
                isLoading = false
            }
        }))
    }

    override fun getSaldoRdn(accountRdn: AccountRdn?): Observable<AccountRdn?>? {
        val seqNum = brImoPrefRepository.seqNumber

        val request = RdnInfoSaldoRequest(accountRdn!!.account)

        return apiSource.getData(urlInfo, request, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.newThread())
                .observeOn(schedulerProvider.mainThread())
                .map<AccountRdn>(object : Function<String?, AccountRdn?> {
                    @Throws(java.lang.Exception::class)
                    override fun apply(stringResponse: String): AccountRdn? {
                        var saldoReponse: SaldoReponse


                        //get checksum response
                        val responseCheck = MapperHelper.getIdResponse(stringResponse)

                        //jika checksum response kosong
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
                            if (responseCheck.isEmpty()) {
                                saldoReponse = SaldoReponse()
                                saldoReponse.balanceString = "-"
                            }
                        }

                        //coba konversi String ke RestResponse model
                        val restResponse = MapperHelper.stringToRestResponse(stringResponse, seqNum)
                        if (restResponse != null) {
                            if (GeneralHelper.isContains(R.array.response_code_success, restResponse.code)) {
                                if (restResponse.desc.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true)) {
                                    saldoReponse = SaldoReponse()
                                    saldoReponse.balanceString = restResponse.desc
                                } else {
                                    saldoReponse = SaldoReponse()
                                    saldoReponse = restResponse.getData(SaldoReponse::class.java)

                                    //jika rekening adalah default maka saldo local diupdate
                                }
                            } else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_12.value) {
                                saldoReponse = SaldoReponse()
                                saldoReponse.balanceString = restResponse.code
                                saldoReponse.name = restResponse.desc
                                view.onExceptionTotalSaldo()
                            } else if (restResponse.code == RestResponse.ResponseCodeEnum.RC_SESSION_END.value) {
                                saldoReponse = SaldoReponse()
                                saldoReponse.balanceString = restResponse.code
                                view.onSessionEnd(restResponse.desc)
                            } else {
                                saldoReponse = SaldoReponse()
                                saldoReponse.balanceString = "-"
                            }
                        } else {
                            saldoReponse = SaldoReponse()
                            saldoReponse.balanceString = "-"
                        }
                        accountRdn!!.saldoReponse = saldoReponse
                        return accountRdn
                    }
                })
    }
}