package id.co.bri.brimo.contract.IPresenter.finansialrek;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KtpFinansialRequest;
import id.co.bri.brimo.models.apimodel.request.KtpRefnumRequest;

public interface IInputDataFinansialPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void setUrlKtp(String urlKtp);

    void getDataFinansial(KtpRefnumRequest ktpRefnumRequest);

    void sendDataKtp(KtpFinansialRequest request);
}
