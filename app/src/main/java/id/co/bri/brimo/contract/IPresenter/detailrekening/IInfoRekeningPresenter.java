package id.co.bri.brimo.contract.IPresenter.detailrekening;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.detailrekening.IInfoRekeningView;
import id.co.bri.brimo.models.apimodel.request.PinFinansialRequest;
import id.co.bri.brimo.models.apimodel.request.notificationsetting.GetListNotificationSettingRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq;

public interface IInfoRekeningPresenter<V extends IMvpView & IInfoRekeningView> extends IMvpPresenter<V> {

    void setUrlStatus(String urlStatus);

    void setUrlDefault(String urlDefault);

    void setUrlFinansialRek(String urlFinansialRek);

    void setUrlBiFast(String url);

    void setUrlDetailStatus(String urlDetail);

    void getStatusKartu(String accNumb);

    void setChangeDefault(String accountNumber);

    void getListBiFast();

    void setUrlInfoSaldoHold(String urlInfoSaldoHold);

    void getInfoSaldoHold();

    void sendFinansialRek(PinFinansialRequest pinRequest, int statusFinansial);

    void getDataDetailStatus(DetailKelolaKartuReq detailKelolaKartuReq);

    void setUrlGetListNotification(String url);

    void getListNotificationSetting(GetListNotificationSettingRequest request);
}