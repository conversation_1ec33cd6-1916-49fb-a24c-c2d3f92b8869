package id.co.bri.brimo.contract.IPresenter.splitbill

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.splitbill.SplitBillDetailResponse
import id.co.bri.brimo.models.splitbill.SplitBillEditFormItemViewModel

interface ISplitBillEditFormPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlProcessBill(urlProcess: String)
    fun setUrlDraftDetails(urlDraftDetails: String)

    fun setItems(splitBillDetailResponse: SplitBillDetailResponse, mIsFromHistory: Boolean)

    fun addItem()

    fun onBillNameChanged(billName: String)
    fun onBillDateChanged(billDate: String)
    fun onTaxAmountChanged(amount: Long)
    fun onServicesAmountChanged(amount: Long)
    fun onTotalAmountChanged(amount: Long)
    fun onDeleteItemProduct(itemModel: SplitBillEditFormItemViewModel)
    fun doValidateButtonSubmit(itemModel: SplitBillEditFormItemViewModel? = null)
    fun onItemChanged(itemModel: SplitBillEditFormItemViewModel)

    fun replaceDotInForm(rawInput: String) : String

    fun getSplitBillProcess()
    fun getSplitBillDraftDetail()

    fun onSaveDraftClicked()

    fun onBillDataEmpty()

    fun onQuantityEmpty(itemModel: SplitBillEditFormItemViewModel)

    fun isBillCreated(): Boolean

}