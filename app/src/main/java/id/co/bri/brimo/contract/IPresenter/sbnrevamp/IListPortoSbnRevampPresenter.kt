package id.co.bri.brimo.contract.IPresenter.sbnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnDetailPortoRequest

interface IListPortoSbnRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlListPortoSbn(urlListPorto: String)

    fun setUrlSbnDetailPorto(urlDetailItemPorto: String)

    fun getListPortoSbn()

    fun getDataDetailPortoSbn(request : SbnDetailPortoRequest)
}