package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.UbahPinRequest

interface IFirstTimeRencanaPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlUpdateRekening(url : String)

    fun getUpdateRekening(request : UbahPinRequest)
}