package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisKycRequest

interface IRegistrasiCameraPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDeviceId()

    fun setUrlCheckKtp(url: String)

    fun setUrlPhotoKtp(url: String)

    fun sendPhotoKtp(regisKycRequest: RegisKycRequest)

    fun sendCheckKtp()
}