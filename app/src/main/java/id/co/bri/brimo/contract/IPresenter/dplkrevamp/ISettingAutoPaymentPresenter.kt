package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.EditAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest

interface ISettingAutoPaymentPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlInquiryAutoPayment(urlAddAutoPayment: String)
    fun setUrlEditAutoPayment(urlEditAutoPayment: String)

    fun editAutoPayment(request: EditAutoPaymentRequest)
    fun getInquiryAutoPayment(request: InquiryAutoPaymentRequest)

}