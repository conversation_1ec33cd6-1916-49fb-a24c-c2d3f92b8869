package id.co.bri.brimo.contract.IPresenter.notificationsetting

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.notificationsetting.ChangeAmountNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.FirstRegistrationNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.ChangeStatusNotificationRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.GetDetailNotificationSettingRequest

interface INotificationSettingDetailPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlGetDetailNotification(url : String)

    fun getDetailNotificationSetting(request: GetDetailNotificationSettingRequest)

    fun setUrlChangeStatus(url: String)

    fun changeStatus(request: ChangeStatusNotificationRequest)

    fun setUrlFirstRegistration(url: String)

    fun firstRegistration(request: FirstRegistrationNotificationRequest)

    fun setUrlChangeAmount(url: String)

    fun changeAmount(request: ChangeAmountNotificationRequest)
}