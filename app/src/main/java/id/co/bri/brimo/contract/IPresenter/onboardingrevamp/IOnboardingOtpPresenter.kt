package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingResendReq
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingValidateReq

interface IOnboardingOtpPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlResend(url: String)

    fun setUrlSend(url: String)

    fun getDeviceId(): String

    fun sendResendOtp(resendRequest: OnboardingResendReq)

    fun sendSendOtp(validateRequest: OnboardingValidateReq)
}