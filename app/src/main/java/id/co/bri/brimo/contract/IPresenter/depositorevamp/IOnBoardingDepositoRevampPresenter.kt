package id.co.bri.brimo.contract.IPresenter.depositorevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IOnBoardingDepositoRevampPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrl(url:String)

    fun getListDepositoAccount()
    fun setTermUrl(url:String)

    fun getTermData()

    fun getRekeningDepositoUpdate(formUrl :String, type:String,   pin:String, isFTU:Boolean)

}