package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.RegisIdModel
import id.co.bri.brimo.models.apimodel.request.RegisSendOtpReq

interface IRegistrasiOtpPrivyPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlSendOtp(url: String)

    fun setUrlResendOtp(url: String)

    fun sendOtp(regisSendOtpReq: RegisSendOtpReq)

    fun resendOtp(deviceIdRequest: RegisIdModel)
}