package id.co.bri.brimo.contract.IPresenter.pascabayar;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryPascaRequest;

public interface IFormPascaPresenter <V extends IBaseFormView>
        extends IBaseFormPresenter<V> {
    void getDataInquiry(InquiryPascaRequest request);
}
