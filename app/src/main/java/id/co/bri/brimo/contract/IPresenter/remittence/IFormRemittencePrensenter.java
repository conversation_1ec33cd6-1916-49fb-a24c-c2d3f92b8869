package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryTelevisiRequest;
import id.co.bri.brimo.models.apimodel.request.LoadDataRequest;

public interface IFormRemittencePrensenter <V extends IBaseFormView>
        extends IBaseFormPresenter<V> {

    void getLoadData(LoadDataRequest loadDataRequest, String saveId);

    void getDataFormTrf();

    void setUrlSaved(String url);

    boolean isTFirstTimeTransferInternasional();
}
