package id.co.bri.brimo.contract.IPresenter.britamarencana;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiEditRequest;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiRencanaRequest;

public interface IKonfirmasiRencanaPresenter  <V extends IMvpView>
        extends IMvpPresenter<V> {
    void setUrl(String url);

    void getKonfirmasiRencana(KonfirmasiRencanaRequest konfirmasiRencanaRequest);

    void getKonfirmasiEditRencana(KonfirmasiEditRequest konfirmasiEditRequest);
}
