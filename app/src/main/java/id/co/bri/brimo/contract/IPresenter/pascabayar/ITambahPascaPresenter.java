package id.co.bri.brimo.contract.IPresenter.pascabayar;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryPascaRequest;
import id.co.bri.brimo.models.apimodel.request.pascaBayarRevamp.AddSavedListPascaRequest;

public interface ITambahPascaPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {
    void getDataInquiry(InquiryPascaRequest request);

    void getDataInquiryAddSavedList(AddSavedListPascaRequest addSavedListPascaRequest);

    void setInquiryUrl(String url);
}
