package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest

interface IOnboardingAutoPaymentRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlInquiryAddAutoPayment(urlAddAutopayment : String)

    fun getInquiryAddPayment(request: InquiryAutoPaymentRequest)

}