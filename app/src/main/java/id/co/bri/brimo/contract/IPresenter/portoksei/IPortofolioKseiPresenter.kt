package id.co.bri.brimo.contract.IPresenter.portoksei

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest
import id.co.bri.brimo.models.apimodel.request.portofolioksei.GetDetailKseiRequest
import id.co.bri.brimo.models.apimodel.response.portofolioksei.ItemAssetResponse

interface IPortofolioKseiPresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun getDataPortofolio()
    fun setUrlPortofolio(url: String?)
    fun setUrlDetail(url:String)
    fun getDataDetail(req : GetDetailKseiRequest)

    fun setUrlRegisKsei(url : String)

    fun getRegisKsei()
}
