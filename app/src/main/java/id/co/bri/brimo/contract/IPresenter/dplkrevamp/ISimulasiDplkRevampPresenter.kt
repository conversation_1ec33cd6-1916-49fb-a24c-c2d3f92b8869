package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryDplkRegisRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ListDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.SimulasiDplkRequest

interface ISimulasiDplkRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlSimulasiDplk(urlSimulasiDplk: String)

    fun setUrlInquiryDplk(urlInquryDplk: String)
    fun setUrlRegistrationDplk(urlRegistrationDplk: String)

    fun getDataSimulasiDplk(request: SimulasiDplkRequest)

    fun getDataInquiryDplk(request: InquiryDplkRegisRequest)
    fun getRegistrationDplk()
}