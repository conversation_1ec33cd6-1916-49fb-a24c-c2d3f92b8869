package id.co.bri.brimo.contract.IPresenter.splitbill

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.models.apimodel.response.splitbill.viewentity.BillEntity

interface ISplitBillHistoryPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setPageType(pageType: Constant.SplitBillHistoryPageType)

    fun setUrlDeleteBillItem(urlDetele: String)

    fun setUrlDetailItem(urlDetail: String)

    fun setUrlHistory(urlHistory: String)

    fun setBills(models: List<BillEntity>)

    fun fetchHistories()

    fun searchHistories(searchQuery: String)

    fun onClickBillItem(billEntity: BillEntity)

    fun onDeleteBillItem(billEntity: BillEntity)

    fun setupFilter()

    fun onClickFilter(filterSplitBillResponse: LifestyleConfig.FilterSplitBill)

}
