package id.co.bri.brimo.security;

import android.content.Context;
import android.util.Log;

import id.co.bri.brimo.R;
import id.co.bri.brimo.di.scopes.ApplicationContext;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;


/*
 * Class MyEncrypt.java
 * Digunakan untuk enkripsi message
 *
 * */
public class MyEncrypt implements IMyEncrypt{

    private Context mContext = null;

    private static final String TAG = "MyEncrypt";

    public MyEncrypt(@ApplicationContext Context mContext) {
        this.mContext = mContext;
    }

    public static String encrypt(Context context, String username, String imsi, String imei, String key) {
        String time = new Date().getTime() + "";
        Log.d(TAG, "time : " + time);
        String plain = username + imei + imsi;
        String keytime = time.substring(0, 10) + key;
        String result = "";
        String result2 = "";
        String result3 = "";

        int counterKey = 0;


        for (int i = 0; i < plain.length(); i++) {
            if (i % 2 == 1 && (counterKey < keytime.length()))
                result += plain.charAt(i) + "" + keytime.charAt(counterKey++);
            else
                result += plain.charAt(i);
        }
        result2 = new StringBuffer(result).reverse().toString();
        result3 = rot(result2);
        String hash = sha256(context, result3).substring(6, 8);
        return result3 + hash;
    }

    private static String rot(String args) {
        String s = "";
        for (int i = 0; i < args.length(); i++) {
            char c = args.charAt(i);
            if (c >= 'a' && c <= 'm')
                c += 13;
            else if (c >= 'n' && c <= 'z')
                c -= 13;
            else if (c >= 'A' && c <= 'M')
                c += 13;
            else if (c >= 'A' && c <= 'Z')
                c -= 13;
            s = s + c;
        }
        return s;
    }


    public static String getHeaderKey(String s) {
        return GeneralHelper.md5(s.concat(AppConfig.getHeaderKey()));
    }

    public static String getAPIKey(Context context, String s, String random) {
        return sha256(context, s + AppConfig.getHeaderKey() + random);
    }

    public static String sha256(Context context, String s) {
        MessageDigest digest = null;
        byte[] hash = null;
        try {
            digest = MessageDigest.getInstance(context.getResources().getString(R.string.sha_256));
        } catch (NoSuchAlgorithmException e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();

             */
        }

        if (digest != null)
            hash = digest.digest(s.getBytes(StandardCharsets.UTF_8));

        return bin2hex(hash);
    }

    static String bin2hex(byte[] data) {
        if(data != null)
            return String.format("%0" + (data.length * 2) + 'x', new BigInteger(1, data));
        else
            return "";
    }

    @Override
    public String sha256(String s) {
        MessageDigest digest = null;
        byte[] hash = null;
        try {
            digest = MessageDigest.getInstance(mContext.getResources().getString(R.string.sha_256));
        } catch (NoSuchAlgorithmException e) {
            /*
            if(BuildConfig.DEBUG)
            e.printStackTrace();

             */
        }

        if (digest != null)
            hash = digest.digest(s.getBytes(StandardCharsets.UTF_8));

        return bin2hex(hash);
    }
}
