package id.co.bri.brimo.contract.IPresenter.loaninapp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.loaninapp.LoanInAppTermRequest
import id.co.bri.brimo.models.apimodel.response.applyccrevamp.ApplyCcSofListResponse
import io.reactivex.Observable

interface ILoanInAppFormPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getAccountWithSaldo()
    fun getFormData(pin: String, cardToken: String)
    fun getTermData(loanInAppTermRequest: LoanInAppTermRequest)
    fun getUsername(): String
    fun getTerm()
    fun getProductList()
    fun getDetailCc(cardToken: String, itemPosition: Int, account: ApplyCcSofListResponse.Account)
}