package id.co.bri.brimo.contract.IPresenter.lupausername;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.GeneralResendOtpRequest;
import id.co.bri.brimo.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;

public interface IVerifikasiOtpEmailPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlValidate(String url);

    void setUrlResend(String url);

    void resendOtp(GeneralResendOtpRequest request);

    void validateOtp(ValidateOtpUserPassReq request, boolean forgetUsername);
}
