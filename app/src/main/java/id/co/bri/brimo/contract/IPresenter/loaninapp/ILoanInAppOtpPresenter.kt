package id.co.bri.brimo.contract.IPresenter.loaninapp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.applyccrevamp.SubmitOtpRequest
import id.co.bri.brimo.models.apimodel.request.loaninapp.LoanInAppSubmitRequest

interface ILoanInAppOtpPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun submitData(loanInAppSubmitRequest: LoanInAppSubmitRequest)
    fun getOnboardingData(withLoading: Boolean)
}
