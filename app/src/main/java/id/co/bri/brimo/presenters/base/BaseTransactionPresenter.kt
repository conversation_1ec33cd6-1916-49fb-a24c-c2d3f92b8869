package id.co.bri.brimo.presenters.base

import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.base.IBaseTransactionPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.base.IBaseTransactionView
import id.co.bri.brimo.contract.IView.pulsarevamp.reskin.AccountModelNs
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.MvpPresenter
import id.co.bri.brimo.util.subscribeWithObserver
import io.reactivex.disposables.CompositeDisposable

abstract class BaseTransactionPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
), IBaseTransactionPresenter<V> where V : IMvpView, V : IBaseTransactionView {
    override fun getAccountList() {
        getView().showProgress()
        val mUrl = GeneralHelper.getString(R.string.url_v5_account_list)

        if (isViewAttached && mUrl != null) {
            val seqNum = brImoPrefRepository.seqNumber
            apiSource.getDataForm(mUrl, seqNum).subscribeWithObserver(
                compositeDisposable = compositeDisposable,
                schedulerProvider = schedulerProvider,
                createObserver = {
                    object: ApiObserver(view, seqNum) {
                        override fun onFailureHttp(errorMessage: String?) {
                            getView().hideProgress()
                        }

                        override fun onApiCallSuccess(response: RestResponse) {
                            getView().hideProgress()
                            val result = response.getData(AccountModelNs::class.java)

                            getView().onSuccessAccountList(result.account.toMutableList())
                        }

                        override fun onApiCallError(restResponse: RestResponse) {
                            getView().hideProgress()
                        }
                    }
                }
            )
        }
    }
}