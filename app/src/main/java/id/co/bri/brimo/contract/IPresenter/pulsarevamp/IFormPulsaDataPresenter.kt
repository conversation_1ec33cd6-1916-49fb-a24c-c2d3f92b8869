package id.co.bri.brimo.contract.IPresenter.pulsarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.SavedResponse

interface IFormPulsaDataPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun getDataForm()

    fun getDataFormFastMenu()

    fun setFormUrl(urlForm: String)

    fun setInquiryUrl(urlInquiry: String)

    fun setKonfirmasiUrl(urlConfirmation: String)

    fun setUpdateItemTf(url: String, savedResponse: SavedResponse, position: Int, type: Int)

}