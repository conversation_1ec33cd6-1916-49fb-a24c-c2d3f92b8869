package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest

interface IHistoryRiwayatProsesClaimDplkPresenter <V : IMvpView?> : IMvpPresenter<V> {

    fun seturlListClaimBrifine(url : String)
    fun setUrlDetailClaimBrifine(url : String)
    fun getListClaimBrifine()
    fun getDetailClaimBrifine(request: DetailKlaimDplkRequest)

}