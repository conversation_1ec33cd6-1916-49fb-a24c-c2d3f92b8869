package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingDataKeuanganReq

interface IOnboardingDataKeuanganPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrl(url: String)

    fun sendFinancialData(onboardingDataKeuanganReq: OnboardingDataKeuanganReq)

    fun getDeviceId() : String
}