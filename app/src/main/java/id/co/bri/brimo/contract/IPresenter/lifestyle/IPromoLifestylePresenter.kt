package id.co.bri.brimo.contract.IPresenter.lifestyle

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.response.DetailPromoResponse

interface IPromoLifestylePresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlDetailPromo(urlPromo: String)

    fun getDetailPromoItem(detailPromoResponse: DetailPromoResponse)

    fun setUrlBusShuttle(urlFormBus: String)

    fun setUrlKai(urlFormKai: String)

    fun setUrlWebviewTugu(urlTugu: String)

    fun getFormBus()

    fun getFormKai(titleBar: String, appType: Int)

    fun getWebViewTugu(
        partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String, appType: Int
    )

    fun getIndihomeRegistrationData(
        partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String, appType: Int
    )

    fun confirmIndihomeRegistration(
        partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String, appType: Int
    )

}