package id.co.bri.brimo.contract.IPresenter.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IOtpNoHpPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlOtpNoHp(url: String)
    fun setUrlOtpEmail(url: String)
    fun setUrlResendOtpNoHp(url: String)
    fun setUrlResendOtpEmail(url: String)

    fun sendOtpNoHp(otp: String)
    fun sendOtpEmail(otp: String)
    fun sendResendOtpNoHp(method: String)
    fun sendResendOtpEmail(email: String)
}