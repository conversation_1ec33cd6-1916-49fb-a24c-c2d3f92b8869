package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import android.content.Intent
import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.ReferralRequest

interface IOnboardingRekeningPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun checkDevice()

    fun generateDeviceId()

    fun setUrlProgress(url: String)

    fun setUrlReferral(url: String)

    fun getProgressOnboarding()

    fun sendReferral(request: ReferralRequest)

    fun checkUsername(intent: Intent)
}