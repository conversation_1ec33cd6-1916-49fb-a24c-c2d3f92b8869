package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingLoginResponse

interface IOnboardingReceiptPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun saveUserLogin(onboardingLogin: OnboardingLoginResponse)

    fun setUrlRevokeLogin(url: String)

    fun sendRevokeLogin()

    fun saveBubbleShowRek(termCondition: String)
}