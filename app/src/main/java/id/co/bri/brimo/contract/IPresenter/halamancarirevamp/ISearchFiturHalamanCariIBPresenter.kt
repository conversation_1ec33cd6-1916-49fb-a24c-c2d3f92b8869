package id.co.bri.brimo.contract.IPresenter.halamancarirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface ISearchFiturHalamanCariIBPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrlGetDataPayload(url: String)

    fun setUrlValidateMiniApp(url: String)

    fun getDataSearch()

    fun getDataDashboardLifestyleMenu(url: String)

    fun validateMiniProgram(miniAppId: String)

    fun getDataPayloadNfc(pin: String)

    fun setUrlChatBanking(urlChatBanking: String?)
    fun onGetChatBanking()
    fun getVoip()
    fun setUrlVoip(url: String?)
    fun setUrlRevoke(urlRevoke: String)

    fun revokeSession()
    fun setRevokeSession(sessionRevoke: String?)

    fun setUrlSplitBill(urlSplitBill: String)
    fun getSplitBill()
}