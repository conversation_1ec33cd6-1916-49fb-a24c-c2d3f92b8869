package id.co.bri.brimo.contract.IPresenter.digitalsaving;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KodePosRequest;

public interface IPilihanKodePosPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {
    void sendSearchKodePos(KodePosRequest kodePosRequest);

    void setUri(String uri);
}