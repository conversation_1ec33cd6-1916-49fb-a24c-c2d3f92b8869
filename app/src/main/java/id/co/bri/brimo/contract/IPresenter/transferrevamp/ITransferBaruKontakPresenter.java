package id.co.bri.brimo.contract.IPresenter.transferrevamp;


import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferKontakRequest;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.SavedDataInquiryRequest;

public interface ITransferBaruKontakPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {
    void getDataInquiry(InquiryTransferKontakRequest inquiryTransferKontakRequest);

    void getDataSavedListInquiry(SavedDataInquiryRequest savedDataInquiryRequest);

    void setInquiryUrl(String url);
}

