package id.co.bri.brimo.contract.IPresenter.openaccount;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.daomodel.Transaksi;

public interface IKonfirmasiGeneralOpenAccountPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataPayment (String pin, GeneralConfirmationResponse response);

    void setUrlPayment(String urlPayment);

    Transaksi generateTransaksiModel(int kategoriId, long amount, String referenceNumber, String billingName, String trxType);

    void onSaveTransaksiPfm(Transaksi transaksi);

}
