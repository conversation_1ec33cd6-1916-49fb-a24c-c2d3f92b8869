package id.co.bri.brimo.contract.IPresenter.autograbfund

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest

interface IDashboardAutoGrabFundPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDataList()
    fun setListUrl(url: String?)

    fun setDeleteUrl(url: String?)
    fun getDelete(idAgf: String, pin: String)
    fun setUpdateStatusUrl(url: String?)
    fun getUpdateStatus(idAgf: String, status: String, pin: String)

}