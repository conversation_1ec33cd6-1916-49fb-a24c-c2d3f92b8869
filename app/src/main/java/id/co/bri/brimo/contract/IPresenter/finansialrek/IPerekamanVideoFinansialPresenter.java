package id.co.bri.brimo.contract.IPresenter.finansialrek;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.VideoFinansialRequest;

public interface IPerekamanVideoFinansialPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void getDeviceId();

    void sendVideoData(VideoFinansialRequest request);
}
