package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.bukarekening.InquiryOpenAccountRequest

interface IPilihTabunganRevPresenter<V : IMvpView?> : IMvpPresenter<V>{

    fun setUrl(url : String)

    fun setUrlInquiry(url : String)

    fun setUrlSafety(url : String)

    fun setUrlFormValas(url : String)

    fun getJenisTabungan()

    fun getInquiryTabungan(request: InquiryOpenAccountRequest)

    fun getPusatBantuanSafety(id : String)

    fun getInquiryTabunganJunio(request: InquiryOpenAccountRequest)

    fun getFormTabunganValas(request: InquiryOpenAccountRequest)

//    fun setUrlValidationS3f(urlValidS3f: String?)
//
//    fun getDataValidationS3F(request : InquiryOpenAccountRequest)
    fun getInquiryRencana(rencana: InquiryOpenAccountRequest)

}