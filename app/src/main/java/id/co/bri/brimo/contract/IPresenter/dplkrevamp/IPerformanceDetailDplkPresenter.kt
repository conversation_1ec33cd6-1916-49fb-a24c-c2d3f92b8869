package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ProductFtuDplkRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailPerformanceTabDplkResponse

interface IPerformanceDetailDplkPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlDetailPerformanceTabDplk(urlDetailPerformanceTab: String)

    fun getDetailPerformanceTabDplk(request: ProductFtuDplkRequest)

    fun saveDataGraphicDplk(response: DetailPerformanceTabDplkResponse)

    fun getDataGraphicDplk(): DetailPerformanceTabDplkResponse
}