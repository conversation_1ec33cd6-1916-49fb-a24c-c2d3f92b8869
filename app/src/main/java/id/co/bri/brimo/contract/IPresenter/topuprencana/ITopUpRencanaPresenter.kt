package id.co.bri.brimo.contract.IPresenter.topuprencana

import android.content.Context
import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.topuprencana.PostTopupPlanRequest

interface ITopUpRencanaPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun getSaldoRekeningUtama(): String

    fun setUrlTopupRencanaConfirmation(url: String)

    fun postTopupRencanaRequest(context: Context, postTopupPlanRequest: PostTopupPlanRequest)
}