package id.co.bri.brimo.contract.IPresenter.pendidikan;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryPdamRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryPendidikanRequest;

public interface ITambahPendidikanPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void getDataInquiry(InquiryPendidikanRequest request);

    void setInquiryUrl(String url);
}
