package id.co.bri.brimo.presenters.pfm;

import id.co.bri.brimo.contract.IPresenter.pfm.ICatatanPengeluaranPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.ICatatanPengeluaranView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.observers.DisposableMaybeObserver;

public class CatatanPengeluaranPresenter<V extends IMvpView & ICatatanPengeluaranView> extends MvpPresenter<V> implements ICatatanPengeluaranPresenter<V> {


    private long totalPengeluaran = 0;

    private DisposableMaybeObserver disposableTransaksi = null;
    private DisposableMaybeObserver disposableMaybeObserver = null;


    public CatatanPengeluaranPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void start() {
        super.start();
    }
}
