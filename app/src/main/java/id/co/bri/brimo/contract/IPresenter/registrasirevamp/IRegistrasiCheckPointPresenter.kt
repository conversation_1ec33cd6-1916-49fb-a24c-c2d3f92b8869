package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.RegisIdModel

interface IRegistrasiCheckPointPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun regisId()
    fun setUrlReset(url: String)
    fun setUrlProgress(url: String)
    fun sendDataReset(regisIdModel: RegisIdModel)
    fun getProgressRegis(progressBack: Boolean)
}