package id.co.bri.brimo.contract.IPresenter.bukaValasRevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.BranchRequest
import id.co.bri.brimo.models.apimodel.request.bukarekening.KonfirmasiValasRequest

interface IInquiryBukaValasRevPresenter<V : IMvpView?> : IMvpPresenter<V> {


    fun setUrlKonfirmasi(url : String)

    fun setUrlPilihKode(url : String)

    fun getKonfirmasi(request: KonfirmasiValasRequest)

    fun getBranchCode(request: BranchRequest)
}