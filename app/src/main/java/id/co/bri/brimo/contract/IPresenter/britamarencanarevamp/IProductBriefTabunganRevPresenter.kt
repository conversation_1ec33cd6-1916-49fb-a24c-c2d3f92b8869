package id.co.bri.brimo.contract.IPresenter.britamarencanarevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.bukarekening.InquiryOpenAccountRequest

interface IProductBriefTabunganRevPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlInquiry(url :String)

    fun setUrlSafety(url :String)

    fun getInquiryTabungan(request: InquiryOpenAccountRequest)

    fun getInquiryTabunganJunio(request: InquiryOpenAccountRequest)

    fun getPusatBantuanSafety(id : String)

    fun getInquiryRencana(rencana: InquiryOpenAccountRequest)

}