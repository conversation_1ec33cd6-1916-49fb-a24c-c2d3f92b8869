package id.co.bri.brimo.contract.IPresenter.openaccount

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.KodePosRequest

interface IOnboardingKodePosBottomPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrl(url: String)

    fun onSendPostCode(kodePosRequest: KodePosRequest)

    fun getDeviceId()
}