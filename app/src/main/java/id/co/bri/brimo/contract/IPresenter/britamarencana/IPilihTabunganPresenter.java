package id.co.bri.brimo.contract.IPresenter.britamarencana;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IPilihTabunganPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void setUrl(String url);

    void getJenisTabungan();

    void getPusatBantuanSafety(String id);

    void setUrlPusatBantuanSafety(String urlPusatBantuanSafety);

}
