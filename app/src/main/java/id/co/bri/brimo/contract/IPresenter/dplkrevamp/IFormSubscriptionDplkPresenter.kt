package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ConfirmDplkRequest

interface IFormSubscriptionDplkPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlConfirmFtuDplk(urlConfirmFtu:String)

    fun getDataConfirmFtuDplk(request: ConfirmDplkRequest)

    fun getDefaultBalance()

}