package id.co.bri.brimo.presenters.pln;

import android.util.Log;

import id.co.bri.brimo.contract.IPresenter.pln.ITambahPlnPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pln.ITambahPlnView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

public class TambahPlnPresenter<V extends IMvpView & ITambahPlnView> extends MvpPresenter<V> implements ITambahPlnPresenter<V> {
    private static final String TAG = "TambahPlnPresenter";
    protected String inquiryUrl;

    public TambahPlnPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

//    @Override
//    public void getDataInquiry(InquiryWalletRequest request) {
//        if(isViewAttached()){
//            GeneralInquiryResponse response = null;
//            getView().onSuccessInquiry(response);
//        }
//
//
//    }

    @Override
    public void getDataInquiry(InquiryPlnRequest request) {
        if (inquiryUrl == null || !isViewAttached()) {
            Log.d(TAG, "getDataInquiry: view atau inquiry urlInformasi null");
            return;
        }

        getView().showProgress();
        String seqNum = getBRImoPrefRepository().getSeqNumber();
        getCompositeDisposable()
                .add(getApiSource().getData(inquiryUrl, request, seqNum)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribeWith(new ApiObserver(getView(),seqNum) {

                            @Override
                            protected void onFailureHttp(String errorMessage) {
                                getView().onException(errorMessage);
                            }

                            @Override
                            protected void onApiCallSuccess(RestResponse response) {
                                //TO-DO onSuccess
                                getView().hideProgress();
                                GeneralInquiryResponse responPln = response.getData(GeneralInquiryResponse.class);
                                getView().onSuccessInquiry(responPln);
                            }

                            @Override
                            protected void onApiCallError(RestResponse restResponse) {
                               onApiError(restResponse);
                            }
                        }));
    }

    @Override
    public void setInquiryUrl(String url) {
        inquiryUrl = url;
    }
}
