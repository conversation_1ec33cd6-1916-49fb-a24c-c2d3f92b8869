package id.co.bri.brimo.contract.IPresenter.saldodompetdigital;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;


public interface IWebViewBindingPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {


    void onSuccessGetCode(String type, String status, String callbackCode, String redirectCode, String authCode);

    void setUrlUpdateStatusBinding(String url);
}
