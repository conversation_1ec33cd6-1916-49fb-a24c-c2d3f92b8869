package id.co.bri.brimo.contract.IPresenter.listrikrevamp

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.request.InquiryPlnRequest

interface IFormListrikRevampPresenter <V : IBaseFormRevampView> : IBaseFormRevampPresenter<V> {
    fun getDataInquiry(request: InquiryPlnRequest, nominal: String)

    fun setInquiryUrl(url: String)
}