package id.co.bri.brimo.contract.IPresenter.rdnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.rdn.InquiryRdnRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnHistoryListWithdrawRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnInquiryWithdrawRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnNewDetailRequest

interface IDetailRdnRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlTopUp(urlTopup: String)
    fun setUrlWithdraw(urlWithdraw: String)
    fun setUrlHistoryRdn(urlHistory: String)
    fun getTopUpRdn(request: InquiryRdnRequest)
    fun getWithdrawRdn(request: RdnInquiryWithdrawRequest)
    fun getWithdrawRdnHistoryList(request: RdnHistoryListWithdrawRequest)
}