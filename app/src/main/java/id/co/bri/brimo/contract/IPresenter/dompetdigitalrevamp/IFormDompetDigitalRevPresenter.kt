package id.co.bri.brimo.contract.IPresenter.dompetdigitalrevamp

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormRevampPresenter
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.models.apimodel.response.SavedResponse

interface IFormDompetDigitalRevPresenter<V : IBaseFormRevampView> : IBaseFormRevampPresenter<V> {

    fun getDataInquirySaved(isFromFastMenu: Boolean, walletCode: String, corpCode: String, purchaseNumber: String)

}