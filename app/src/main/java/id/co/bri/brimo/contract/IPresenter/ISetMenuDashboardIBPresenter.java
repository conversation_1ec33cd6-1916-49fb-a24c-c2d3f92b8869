package id.co.bri.brimo.contract.IPresenter;

import java.util.List;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuFeatureModel;
import id.co.bri.brimo.models.daomodel.DashboardMenu.SubFeatureModel;

public interface ISetMenuDashboardIBPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlIbbiz(String urlIbbiz);

    void onGetDataMenuFav();

    void onGetDataMenuAll();

    void onGetDataSearch();

    void onDeleteMenuFavorite(List<SubFeatureModel> menuFavList);

    void onSaveMenuFavorite(List<SubFeatureModel> menuFavList);

    void onDeleteMenuAll(List<MenuFeatureModel> menuFeatureModelList);

    void onSaveMenuAll(List<MenuFeatureModel> menuFeatureModelList);

    void onGetDataIbbiz();
}