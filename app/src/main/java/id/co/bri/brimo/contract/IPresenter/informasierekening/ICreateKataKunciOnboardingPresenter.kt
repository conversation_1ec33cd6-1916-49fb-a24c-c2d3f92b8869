package id.co.bri.brimo.contract.IPresenter.onboardingnewskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface ICreateKataKunciOnboardingPresenter<V : IMvpView> : IMvpPresenter<V> {

    fun onCreateKataKunciSubmit()

    fun setValidateKataKunciUrl(url: String)

    fun setValidateOldKataKunciUrl(url: String)

    fun confirmKataKunci()

    fun isInputValid(): <PERSON><PERSON><PERSON>
}