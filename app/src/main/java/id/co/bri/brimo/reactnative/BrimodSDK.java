package id.co.bri.brimo.reactnative;

import static id.co.bri.brimo.models.apimodel.response.RestResponse.CODE_SESSION_END;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.brimodsdk.RNBrimodSDKDelegate;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity;
import id.co.bri.brimo.ui.activities.ReactNativeActivity;

/**
 * BrimodSDK
 * <p>
 * Main React Native bridge for BRImo Android. This class provides API calls, navigation, event emitters,
 * and other utilities to ensure consistency with the iOS implementation. All main methods are accessible from JS.
 * <p>
 * Main features:
 * - requestApiCall: Call backend APIs from React Native
 * - Navigation to native and React Native screens
 * - Two-way event emitter (send data to React Native & native)
 * - Dismiss React Native screens from JS
 * - Helper for dynamic JSON parsing
 */
public class BrimodSDK implements RNBrimodSDKDelegate {
    private final ExecutorService executor = Executors.newSingleThreadExecutor();
    private final ApiSource apiSource;
    private final BRImoPrefSource prefSource;
    private final Context context;

    private static Map<String, String> urlMap = null;
    private static final String TAG = "BrimodSDK";
    public static final String COMPONENT_NAME_KEY = "component_name";
    public static final String INITIAL_PROPS_KEY = "initial_props";
    private static Map<String, Map<String, String>> nativeScreenMap = null;
    private static Map<String, java.util.List<String>> reactScreenMap = null;

    // Event names
    private static final String EVENT_SEND_DATA_TO_REACT = "sendDataToReact";
    private static final String EVENT_SEND_DATA_TO_NATIVE = "sendDataToNative";

    // OTA Constants
    public static final String OTA_VERSION_CHECK_URL = "https://brimo-ota-service.feedloop.ai/latest-bundle-name";
    public static final String OTA_BUNDLE_DOWNLOAD_URL = "https://brimo-ota-service.feedloop.ai/download";

    // Demo/Development Mode Config
    private static boolean IS_FOR_DEMO = true; // Default is true, can be changed via setter

    public static boolean isForDemo() {
        return IS_FOR_DEMO;
    }

    public static void setForDemo(boolean value) {
        IS_FOR_DEMO = value;
    }

    private static final AtomicBoolean isSessionEndHandled = new AtomicBoolean(false);

    public static void resetSessionEndHandled() {
        isSessionEndHandled.set(false);
    }

    public BrimodSDK(Context context, ApiSource apiSource, BRImoPrefSource prefSource) {
        this.context = context;
        this.apiSource = apiSource;
        this.prefSource = prefSource;
        if (urlMap == null) {
            urlMap = loadUrlMapFromAssets(context);
        }
        if (nativeScreenMap == null) {
            nativeScreenMap = loadNativeScreenMapFromAssets(context);
        }
        if (reactScreenMap == null) {
            reactScreenMap = loadReactScreenMapFromAssets(context);
        }
    }

    private Map<String, String> loadUrlMapFromAssets(Context context) {
        Map<String, String> map = new HashMap<>();
        try {
            InputStream is = context.getAssets().open("data/url_data.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            String json = new String(buffer, StandardCharsets.UTF_8);
            Gson gson = new Gson();
            Type type = new TypeToken<Map<String, Map<String, String>>>(){}.getType();
            Map<String, Map<String, String>> rawMap = gson.fromJson(json, type);
            for (Map.Entry<String, Map<String, String>> entry : rawMap.entrySet()) {
                Map<String, String> valueObj = entry.getValue();
                if (valueObj != null && valueObj.containsKey("android")) {
                    map.put(entry.getKey(), valueObj.get("android"));
                }
            }
            Log.d(TAG, "url_data.json loaded successfully, keys: " + map.keySet());
        } catch (Exception e) {
            Log.e(TAG, "Failed to load url_data.json: " + e.getMessage(), e);
        }
        return map;
    }

    private Map<String, Map<String, String>> loadNativeScreenMapFromAssets(Context context) {
        Map<String, Map<String, String>> map = new HashMap<>();
        try {
            InputStream is = context.getAssets().open("data/native_screen_data.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            String json = new String(buffer, StandardCharsets.UTF_8);
            Gson gson = new Gson();
            map = gson.fromJson(json, Map.class);
            Log.d(TAG, "native_screen_data.json loaded successfully, keys: " + map.keySet());
        } catch (Exception e) {
            Log.e(TAG, "Failed to load native_screen_data.json: " + e.getMessage(), e);
        }
        return map;
    }

    private Map<String, java.util.List<String>> loadReactScreenMapFromAssets(Context context) {
        Map<String, java.util.List<String>> map = new HashMap<>();
        try {
            InputStream is = context.getAssets().open("data/react_screen_data.json");
            int size = is.available();
            byte[] buffer = new byte[size];
            is.read(buffer);
            is.close();
            String json = new String(buffer, StandardCharsets.UTF_8);
            Gson gson = new Gson();
            java.lang.reflect.Type type = new com.google.gson.reflect.TypeToken<Map<String, java.util.List<String>>>(){}.getType();
            map = gson.fromJson(json, type);
            Log.d(TAG, "react_screen_data.json loaded successfully, keys: " + map.keySet());
        } catch (Exception e) {
            Log.e(TAG, "Failed to load react_screen_data.json: " + e.getMessage(), e);
        }
        return map;
    }

    @Override
    public void requestApiCall(String apiName, Map<String, Object> payload, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        executor.execute(() -> {
            try {
                if (urlMap == null) {
                    urlMap = loadUrlMapFromAssets(context);
                }
                String encryptedUrl = urlMap.get(apiName);
                if (encryptedUrl == null || encryptedUrl.isEmpty()) {
                    onError.onError("INVALID_API_NAME", "API name '" + apiName + "' not found in url_data", null);
                    return;
                }
                Object requestData;
                if (payload == null || payload.isEmpty()) {
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.addProperty("", "");
                    requestData = jsonObject;
                } else {
                    requestData = new Gson().toJsonTree(payload);
                }
                Gson gson = new Gson();
                String seqNum = prefSource.getSeqNumber();
                io.reactivex.Observable<String> apiCall;
                if (payload == null || payload.isEmpty()) {
                    apiCall = apiSource.getDataTanpaRequest(encryptedUrl, seqNum);
                } else {
                    apiCall = apiSource.getData(encryptedUrl, requestData, seqNum);
                }
                apiCall
                        .timeout(30, TimeUnit.SECONDS)
                        .subscribeOn(io.reactivex.schedulers.Schedulers.io())
                        .observeOn(io.reactivex.android.schedulers.AndroidSchedulers.mainThread())
                        .subscribe(
                                response -> {
                                    try {
                                        RestResponse restResponse = MapperHelper.stringToRestResponse(response, seqNum);
                                        if (restResponse == null) {
                                            onError.onError("API_ERROR", "Response validation failed", null);
                                            return;
                                        }

                                        Log.i(TAG, "Response Code: " + restResponse.getCode(), null);
                                        if ("00".equals(restResponse.getCode()) || "01".equals(restResponse.getCode())) {
                                            try {
                                                String jsonResponse = gson.toJson(restResponse.getData());
                                                onSuccess.onSuccess(jsonResponse);
                                            } catch (Exception e) {
                                                Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                                                onError.onError("JSON_ERROR", "Failed to serialize response data", e);
                                            }
                                        } else if (CODE_SESSION_END.equals(restResponse.getCode())) {
                                            if (isSessionEndHandled.compareAndSet(false, true)) {
                                                FastMenuNewSkinActivity.launchIntent(currentActivity);
                                            }
                                            String errorMessage = "API returned error code: " + restResponse.getCode() + " - " + restResponse.getDesc();
                                            Log.i(TAG, "Error message: " + errorMessage, null);
                                            onError.onError("UNAUTHORIZED", errorMessage, null);
                                        } else {
                                            String errorMessage = "API returned error code: " + restResponse.getCode() + " - " + restResponse.getDesc();
                                            Log.i(TAG, "Error message: " + errorMessage, null);
                                            onError.onError("API_ERROR", errorMessage, null);
                                        }
                                    } catch (Exception e) {
                                        Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                                        onError.onError("JSON_ERROR", "Failed to parse API response: " + e.getMessage(), e);
                                    }
                                },
                                error -> {
                                    String errorMessage = error != null ? error.getMessage() : "Unknown network error";
                                    Log.i(TAG, "Error message: " + errorMessage, null);
                                    onError.onError("NETWORK_ERROR", errorMessage, error);
                                }
                        );
            } catch (Exception e) {
                Log.i(TAG, "Error message: " + e.getMessage().toString(), null);
                onError.onError("BRIDGE_ERROR", "Bridge execution error: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public void selectTabBarItem(int index, Map<String, Object> params, OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Log.i("ReactNativeFragment",params.toString());
        if (currentActivity instanceof DashboardIBActivity) {
            ((DashboardIBActivity) currentActivity).selectTabByIndex(index, params);
            onSuccess.onSuccess("success");
        } else {
            onError.onError("NO_DASHBOARD", "Dashboard activity not found", null);
        }
    }

    @Override
    public void navigateToNative(String name, Map<String, Object> params, boolean isRnDismissed, Activity currentActivity) {
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            try{
                String activityName;
                if (nativeScreenMap != null && nativeScreenMap.containsKey(name)) {
                    Map<String, String> screenInfo = nativeScreenMap.get(name);
                    activityName = screenInfo.get("activityName");
                } else if (name != null && name.contains(".")) {
                    activityName = name;
                } else {
                    activityName = null;
                }
                if (activityName == null || activityName.isEmpty()) return;
                if (isRnDismissed && currentActivity instanceof ReactNativeActivity) {
                    currentActivity.finish();
                    new Handler().postDelayed(() -> openNativeActivity(activityName, params), 300);
                } else {
                    openNativeActivity(activityName, params);
                }
            } catch (Exception e) {
                // log error if needed
            }
        });
    }

    @Override
    public void navigateToReact(String bundleName, String appName, Map<String, Object> params, Activity currentActivity) {
        try {
            if (reactScreenMap == null) {
                reactScreenMap = loadReactScreenMapFromAssets(context);
            }
            java.util.List<String> appList = reactScreenMap.get(bundleName);
            if (appList == null || !appList.contains(appName)) {
                return;
            }
            Intent intent = new Intent(currentActivity != null ? currentActivity : context, ReactNativeActivity.class);
            intent.putExtra(COMPONENT_NAME_KEY, appName);
            if (params != null) {
                Bundle bundle = mapToBundle(params);
                if (bundle != null) {
                    intent.putExtra(INITIAL_PROPS_KEY, bundle);
                }
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (currentActivity != null) {
                currentActivity.startActivity(intent);
            } else {
                context.startActivity(intent);
            }
        } catch (Exception e) {
            // log error if needed
        }
    }

    @Override
    public void sendDataToNative(String name, Map<String, Object> data) {
        Intent intent = new Intent(name);
        if (data != null) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue().toString());
            }
        }
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    @Override
    public void dismiss(OnSuccessCallback onSuccess, OnErrorCallback onError, Activity currentActivity) {
        Handler mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(() -> {
            if (currentActivity instanceof ReactNativeActivity) {
                currentActivity.finish();
                onSuccess.onSuccess("dismissed");
            } else {
                onError.onError("NO_REACT_NATIVE_ACTIVITY", "No ReactNativeActivity to dismiss", null);
            }
        });
    }

    @Override
    public void getValueSharePref(String name, String key, OnSuccessCallback onSuccess, OnErrorCallback onError) {
        try {
            Log.d(TAG, "getValueSharePref: name=" + name + ", key=" + key);
            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            String value = sp.getString(key, "");
            Log.d(TAG, "getValueSharePref: value=" + value);
            onSuccess.onSuccess(value);
        } catch (Exception e) {
            Log.e(TAG, "getValueSharePref error: " + e.getMessage(), e);
            onError.onError("SHAREDPREF_ERROR", e.getMessage(), e);
        }
    }

    @Override
    public void saveDataSharePref(String name, String key, String value) {
        try {
            Log.d(TAG, "saveDataSharePref: name=" + name + ", key=" + key + ", value=" + value);
            SharedPreferences sp = context.getSharedPreferences(name, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sp.edit();
            editor.putString(key, value);
            editor.apply();
            Log.d(TAG, "saveDataSharePref: apply() called");
        } catch (Exception e) {
            Log.e(TAG, "saveDataSharePref error: " + e.getMessage(), e);
        }
    }

    private void openNativeActivity(String activityName, Map<String, Object> params) {
        try {
            if (activityName == null || activityName.isEmpty()) return;
            Class<?> clazz = Class.forName(activityName);
            if (!Activity.class.isAssignableFrom(clazz)) return;
            Intent intent = new Intent(context, clazz);
            if (params != null) {
                Bundle bundle = mapToBundle(params);
                if (bundle != null) {
                    intent.putExtras(bundle);
                }
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            // log error if needed
        }
    }

    private Bundle mapToBundle(Map<String, Object> map) {
        Bundle bundle = new Bundle();
        if (map == null) return bundle;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            String key = entry.getKey();
            if (value instanceof Integer) {
                bundle.putInt(key, (Integer) value);
            } else if (value instanceof Boolean) {
                bundle.putBoolean(key, (Boolean) value);
            } else if (value instanceof Double) {
                bundle.putDouble(key, (Double) value);
            } else if (value instanceof Long) {
                bundle.putLong(key, (Long) value);
            } else {
                bundle.putString(key, value != null ? value.toString() : null);
            }
        }
        return bundle;
    }


}