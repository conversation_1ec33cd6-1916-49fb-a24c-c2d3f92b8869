package id.co.bri.brimo.presenters.pfm;

import java.util.concurrent.TimeUnit;

import id.co.bri.brimo.contract.IPresenter.pfm.ICatatanKeuanganDetailPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.pfm.ICatatanKeuanganDetailView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.PFMMessageResponse;
import id.co.bri.brimo.models.apimodel.request.DeletePFMRequest;
import id.co.bri.brimo.models.apimodel.request.FastDeletePFMRequest;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.presenters.MvpPresenter;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.CompositeDisposable;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by user on 06/01/2021
 */
public class CatatanKeuanganDetailPresenter<V extends IMvpView & ICatatanKeuanganDetailView>
        extends MvpPresenter<V> implements ICatatanKeuanganDetailPresenter<V> {

    private static final String TAG = "CatatanKeuanganDetailPr";
    private String urlPFM;
    protected Object fastDeletePFMRequest = null;

    public CatatanKeuanganDetailPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable,
                                          BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource,
                                          TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource);
    }

    @Override
    public void setUrl(String url) {
        urlPFM = url;
    }

    @Override
    public void deletePfm(String trxRefnum, boolean isFromFastMenu) {
        if (getView() != null) {
            String seqNum = getBRImoPrefRepository().getSeqNumber();
            getView().showProgress();

            if (isFromFastMenu) {
                fastDeletePFMRequest = new FastDeletePFMRequest(getFastMenuRequest(), trxRefnum);
            } else {
                fastDeletePFMRequest = new DeletePFMRequest(trxRefnum);
            }

            getCompositeDisposable()
                    .add(getApiSource().getData(urlPFM, fastDeletePFMRequest, seqNum)
                            .timeout(AppConfig.TIMEOUT_CONNECT, TimeUnit.SECONDS)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(new ApiObserver(getView(), seqNum) {

                                @Override
                                protected void onFailureHttp(String errorMessage) {
                                    getView().hideProgress();
                                    getView().onException(errorMessage);
                                }

                                @Override
                                protected void onApiCallSuccess(RestResponse response) {
                                    getView().hideProgress();
                                    PFMMessageResponse data = response.getData(PFMMessageResponse.class);
                                    getView().deletePfmSuccess(data);
                                }

                                @Override
                                protected void onApiCallError(RestResponse restResponse) {
                                    getView().hideProgress();
                                    if (restResponse.getCode().equals(RestResponse.ResponseCodeEnum.RC_SESSION_END.getValue()))
                                        getView().onSessionEnd(restResponse.getDesc());
                                    else
                                        getView().onException(restResponse.getDesc());
                                }
                            }));
        }
    }
}