package id.co.bri.brimo.adapters.dompetdigital

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemHubungkanDompetDigitalReskinBinding
import id.co.bri.brimo.databinding.ListItemSaldoDompetDigitalReskinBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse

class ConnectedWalletsGridAdapter(
    private val context: Context,
    private var connectedWallets: MutableList<EwalletProductResponse>,
    private var balanceResponses: MutableList<EwalletBalanceResponse>,
    private val listener: OnWalletClickListener
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_WALLET = 0
        private const val VIEW_TYPE_CONNECT_BUTTON = 1
    }

    interface OnWalletClickListener {
        fun onWalletClick(wallet: EwalletProductResponse)
        fun onConnectButtonClick()
    }

    fun updateWallets(wallets: List<EwalletProductResponse>) {
        connectedWallets.clear()
        connectedWallets.addAll(wallets)
    }

    fun updateBalances(balances: List<EwalletBalanceResponse>) {
        balanceResponses.clear()
        balanceResponses.addAll(balances)
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < connectedWallets.size) {
            VIEW_TYPE_WALLET
        } else {
            VIEW_TYPE_CONNECT_BUTTON
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_WALLET -> {
                val binding = ListItemSaldoDompetDigitalReskinBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                WalletViewHolder(binding)
            }
            VIEW_TYPE_CONNECT_BUTTON -> {
                val binding = ItemHubungkanDompetDigitalReskinBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ConnectButtonViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is WalletViewHolder -> {
                val wallet = connectedWallets[position]
                holder.bind(wallet, balanceResponses, listener)
            }
            is ConnectButtonViewHolder -> {
                holder.bind(listener)
            }
        }
    }

    override fun getItemCount(): Int {
        // Connected wallets + 1 for the connect button
        val count = connectedWallets.size + 1
        return count
    }

    class WalletViewHolder(private val binding: ListItemSaldoDompetDigitalReskinBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(
            wallet: EwalletProductResponse,
            balanceResponses: List<EwalletBalanceResponse>,
            listener: OnWalletClickListener
        ) {
            // Load wallet icon
            GeneralHelper.loadIconTransaction(
                binding.root.context,
                wallet.iconPath,
                wallet.iconName?.split(".")?.get(0) ?: "",
                binding.imgBanner,
                GeneralHelper.getImageId(binding.root.context, "ic_menu_qna_dompet_digital")
            )

            // Find balance for this wallet
            val balance = balanceResponses.find { it.type.equals(wallet.type, ignoreCase = true) }

            // Set balance in tv_title
            if (balance != null) {
                binding.tvTitle.text = balance.value_string ?: "Rp 0"
            } else {
                binding.tvTitle.text = "Rp 0"
            }

            // Set wallet name in tv_action_dompet_digital
            binding.tvActionDompetDigital.text = wallet.title ?: ""

            // Set click listener
            binding.root.setOnClickListener {
                listener.onWalletClick(wallet)
            }
        }
    }

    class ConnectButtonViewHolder(private val binding: ItemHubungkanDompetDigitalReskinBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(listener: OnWalletClickListener) {
            binding.btnConnect.setOnClickListener {
                listener.onConnectButtonClick()
            }
        }
    }
}
