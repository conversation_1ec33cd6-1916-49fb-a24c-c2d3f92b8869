package id.co.bri.brimo.adapters.dompetdigital

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemHubungkanDompetDigitalReskinBinding
import id.co.bri.brimo.databinding.ListItemSaldoDompetDigitalReskinBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.EwalletBalanceResponse
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse

class ConnectedWalletsGridAdapter(
    private val context: Context,
    private var connectedWallets: MutableList<EwalletProductResponse>,
    private var balanceResponses: MutableList<EwalletBalanceResponse>,
    private val listener: OnWalletClickListener
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_WALLET = 0
        private const val VIEW_TYPE_CONNECT_BUTTON = 1
    }

    interface OnWalletClickListener {
        fun onWalletClick(wallet: EwalletProductResponse)
        fun onConnectButtonClick()
    }

    fun updateWallets(wallets: List<EwalletProductResponse>) {
        connectedWallets.clear()
        connectedWallets.addAll(wallets)
    }

    fun updateBalances(balances: List<EwalletBalanceResponse>) {
        balanceResponses.clear()
        balanceResponses.addAll(balances)
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < connectedWallets.size) {
            VIEW_TYPE_WALLET
        } else {
            VIEW_TYPE_CONNECT_BUTTON
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_WALLET -> {
                val binding = ListItemSaldoDompetDigitalReskinBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                WalletViewHolder(binding)
            }
            VIEW_TYPE_CONNECT_BUTTON -> {
                val binding = ItemHubungkanDompetDigitalReskinBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ConnectButtonViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is WalletViewHolder -> {
                val wallet = connectedWallets[position]
                holder.bind(wallet, balanceResponses, listener)
            }
            is ConnectButtonViewHolder -> {
                holder.bind(listener)
            }
        }
    }

    override fun getItemCount(): Int {
        // Connected wallets + 1 for the connect button
        val count = connectedWallets.size + 1
        return count
    }

    class WalletViewHolder(private val binding: ListItemSaldoDompetDigitalReskinBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(
            wallet: EwalletProductResponse,
            balanceResponses: List<EwalletBalanceResponse>,
            listener: OnWalletClickListener
        ) {
            Log.d("ConnectedWalletsAdapter", "=== BINDING WALLET ===")
            Log.d("ConnectedWalletsAdapter", "Wallet title: ${wallet.title}")
            Log.d("ConnectedWalletsAdapter", "Wallet type: ${wallet.type}")
            Log.d("ConnectedWalletsAdapter", "Wallet iconPath: ${wallet.iconPath}")
            Log.d("ConnectedWalletsAdapter", "Wallet iconName: ${wallet.iconName}")
            Log.d("ConnectedWalletsAdapter", "Balance responses count: ${balanceResponses.size}")

            balanceResponses.forEachIndexed { index, balance ->
                Log.d("ConnectedWalletsAdapter", "Balance[$index] type: '${balance.type}', value_string: '${balance.value_string}'")
            }

            // Load wallet icon
            Log.d("ConnectedWalletsAdapter", "Loading icon with path: ${wallet.iconPath}, name: ${wallet.iconName}")
            GeneralHelper.loadIconTransaction(
                binding.root.context,
                wallet.iconPath,
                wallet.iconName?.split(".")?.get(0) ?: "",
                binding.imgBanner,
                GeneralHelper.getImageId(binding.root.context, "ic_menu_qna_dompet_digital")
            )

            // Find balance for this wallet with more robust matching
            val balance = balanceResponses.find { balanceResponse ->
                when {
                    // Exact match (case insensitive)
                    balanceResponse.type.equals(wallet.type, ignoreCase = true) -> true
                    // Trim whitespace and try again
                    balanceResponse.type.trim().equals(wallet.type?.trim(), ignoreCase = true) -> true
                    else -> false
                }
            }

            Log.d("ConnectedWalletsAdapter", "Found balance: ${balance?.value_string ?: "null"}")

            // Set balance in tv_title
            if (balance != null && !balance.value_string.isNullOrEmpty()) {
                binding.tvTitle.text = balance.value_string
                Log.d("ConnectedWalletsAdapter", "Set balance text to: ${balance.value_string}")
            } else {
                binding.tvTitle.text = "Rp 0"
                Log.d("ConnectedWalletsAdapter", "Set balance text to: Rp 0")
            }

            // Set wallet name in tv_action_dompet_digital
            binding.tvActionDompetDigital.text = wallet.title ?: ""
            Log.d("ConnectedWalletsAdapter", "Set wallet name to: ${wallet.title}")

            // Set click listener
            binding.root.setOnClickListener {
                listener.onWalletClick(wallet)
            }

            Log.d("ConnectedWalletsAdapter", "=== END BINDING ===")
        }
    }

    class ConnectButtonViewHolder(private val binding: ItemHubungkanDompetDigitalReskinBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(listener: OnWalletClickListener) {
            binding.btnConnect.setOnClickListener {
                listener.onConnectButtonClick()
            }
        }
    }
}
