package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.CheckStatusDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ListDplkRequest

interface IFirstTimeDPLKPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrl(url: String)
    fun seturlDplkRegis(url: String)

    fun setUrlDashboardDplk(urlDashboard: String)

    fun setUrlCheckStatusDplk(url: String)

    fun getDashboardDplk()

    fun getDataListBrifine(request: ListDplkRequest)

    fun setUrlPersonalDataRegistration(url: String)

    fun setUrlListBrifine(url: String)

    fun getPersonalDataRegistration()

    fun getCheckStatusDplk(request: CheckStatusDplkRequest)

}