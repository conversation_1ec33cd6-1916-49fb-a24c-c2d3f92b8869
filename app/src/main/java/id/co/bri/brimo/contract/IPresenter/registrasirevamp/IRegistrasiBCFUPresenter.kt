package id.co.bri.brimo.contract.IPresenter.registrasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.RegisNoAtmRequest

interface IRegistrasiBCFUPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlAtm(url: String)

    fun sendNoAtm(regisNoAtmRequest: RegisNoAtmRequest)

    fun regisId()
}