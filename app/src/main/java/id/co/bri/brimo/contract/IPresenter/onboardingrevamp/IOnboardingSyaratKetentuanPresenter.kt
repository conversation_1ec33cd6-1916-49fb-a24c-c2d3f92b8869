package id.co.bri.brimo.contract.IPresenter.onboardingrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingTermCheckboxReq

interface IOnboardingSyaratKetentuanPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrl(url: String)

    fun sendDataConfirm(onboardingTermCheckboxReq: OnboardingTermCheckboxReq)

    fun getDeviceId(): String
}