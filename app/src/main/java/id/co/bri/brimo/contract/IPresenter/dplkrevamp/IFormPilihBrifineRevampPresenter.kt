package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ConfirmationRegisDplkRequest
import id.co.bri.brimo.models.apimodel.request.rdn.KonfirmasiRdnRequest

interface IFormPilihBrifineRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlConfirm(urlConfirmation : String)

    fun getDataConfirmation(
            confirmationRequest: ConfirmationRegisDplkRequest
    )
    fun getDefaultSaldo()
}