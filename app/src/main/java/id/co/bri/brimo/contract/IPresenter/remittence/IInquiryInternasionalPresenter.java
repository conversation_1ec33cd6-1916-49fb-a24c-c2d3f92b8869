package id.co.bri.brimo.contract.IPresenter.remittence;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryInternasionalRequest;
import id.co.bri.brimo.models.apimodel.request.JalurRequest;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiInternasionalRequest;

public interface IInquiryInternasionalPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrl(String url);

    void getKonfirmasiInternational(KonfirmasiInternasionalRequest konfirmasiInternasionalRequest);
}
