package id.co.bri.brimo.contract.IPresenter.registrasiib.nds;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.OtpRegisNdsRequest;

public interface IRegisOtpNdsPresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlOtp(String url);

    void sendDataOtp(OtpRegisNdsRequest otpRegisNdsRequest);
}
