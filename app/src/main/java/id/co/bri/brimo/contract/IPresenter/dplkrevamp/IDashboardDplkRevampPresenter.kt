package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailBrifineDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.FormTopupDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ListDplkRequest

interface IDashboardDplkRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlDashboardDplk(urlDashboard: String)

    fun setUrlDetailDplk(urlDetail: String)

    fun setUrlListBrifine(urlListBrifine: String)

    fun seturlDplkRegis(url: String)

    fun seturlListKlaimBrifine(url: String)

    fun setUrlPersonalDataRegistration(url: String)

    fun setInquiryUrlTopUp(url: String)

    fun getDashboardDplk()

    fun getDetailDplk(request: DetailBrifineDplkRequest)

    fun getDataListBrifine(request: ListDplkRequest)

    fun getDplkRegis()

    fun getListKlaimBrifine()

    fun getDataInquiry(request : FormTopupDplkRequest)

    fun getClaimDplkUrl(url : String)
    fun getDetailClaimBrifine(request: DetailKlaimDplkRequest)

    fun getPersonalDataRegistration()

}