package id.co.bri.brimo.contract.IPresenter.britamarencana;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryEdiRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryOpenRequest;

public interface IHitungRencanaPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void setUrl(String url);

    void setUrlInquiry(String url);

    void getFormBukaRencana();

    void getInquiryOpenRencana(InquiryOpenRequest inquiryOpenRequest);

    void getFormEditRencana();

    void getInquiryEditRencana(InquiryEdiRequest inquiryEdiRequest);
}
