package id.co.bri.brimo.contract.IPresenter.donasirevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.donasirevamp.ConfirmationDonasiRevampRequest

interface IInquiryDonasiRevampPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun getDataConfirmation(request: ConfirmationDonasiRevampRequest)
    fun setUrlConfirmation(url: String?)
    fun getAccountDefault():String
    fun getSaldoRekeningUtama():String
}