package id.co.bri.brimo.contract.IPresenter.pusatBantuan;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IPusatBantuanPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void getPusatBantuan();

    void setFormUrl(String formUrl);

    void setDetailUrl (String detailUrl);

    void setSearchUrl (String searchUrl);

    void getTopicQuestion(String id);

    void getSearch(String keyeword);
}
