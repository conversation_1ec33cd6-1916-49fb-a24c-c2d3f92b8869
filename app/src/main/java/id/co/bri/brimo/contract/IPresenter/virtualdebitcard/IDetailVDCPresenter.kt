package id.co.bri.brimo.contract.IPresenter.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.domain.SnackBarPosition
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.EditLabelVDCRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.EnableDisableTransactionVDCRequest
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.GenerateCvvVDCRequest

interface IDetailVDCPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlGenerateCVV(url: String)

    fun generateCVV(request: GenerateCvvVDCRequest)

    fun setUrlEnableDisableTransactionVDC(url: String)

    fun enableDisableTransactionVDC(request: EnableDisableTransactionVDCRequest)

    fun setUrlDetailVDC(url : String)

    fun getDetailVDC(cardNumber : String, snackBarResponse: String)

    fun setUrlEditLabelVDC(url : String)

    fun updateLabelVDC(request: EditLabelVDCRequest)

    fun getChangePinRefNum(url: String?, cardNumber: String?)
}