package id.co.bri.brimo.contract.IPresenter.sbnrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.esbn.RegisKemenkeuRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnSimulasiRequest

interface IBeliSbnRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlSimulasiSbn(urlSimulasi : String)

    fun setUrlTentangSbn(urlTentang : String)

    fun setUrlValidateUser(urlValidate : String)

    fun setUrlKemenkeu(urlKemenkeu : String)

    fun setUrlRegistrasiSbn(urlRegisSbn : String)
    fun getDataSimulasiSbn(request : SbnSimulasiRequest,idSeri : Int)
    fun setUrlGetDetail(urlGetDetail: String)

    fun setUrlProductsBrief(urlProductBrief: String)
    fun getAboutSbn()

    fun getDetailOffer(idSeri: Int)

    fun getValidateUser(idSeri: Int)

    fun getKemenkeuData(regisKemenkeuRequest: RegisKemenkeuRequest)

    fun getSbnRegisData()

    fun getOnProductBrief()



}