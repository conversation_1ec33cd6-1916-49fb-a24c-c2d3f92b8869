package id.co.bri.brimo.contract.IPresenter.birthdayonboarding

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.BirthdayOnboardingRequest

interface IBirthdayOnboardingPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlGetBirthdayOnboarding(url : String)

    fun getBirthdayOnboarding(request: BirthdayOnboardingRequest)
}