package id.co.bri.brimo.contract.IPresenter.virtualdebitcard

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.CreateVDCRequest

interface IConfirmationCreateVDCPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlCreateVDC(url : String)

    fun createVDC(request: CreateVDCRequest)
}