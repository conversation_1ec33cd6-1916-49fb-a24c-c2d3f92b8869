package id.co.bri.brimo.contract.IPresenter.lifestyle

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.ConfirmationLifestyleResponse

interface IKonfirmasiLifestylePresenter<V : IMvpView> : IMvpPresenter<V> {

    fun setUrlPayment(url: String)

    fun getDataPayment(confirmationLifestyleResponse: ConfirmationLifestyleResponse,
                       account: String, pin: String, pfmCategory: String)

    fun getAccountDefault():String

    fun getSaldoRekeningUtama():String

    fun setUrlGetCashback(url: String)

    fun setRedeemCashbackUrl(url: String)

    fun getCashbackAll(accountNumber: String, referenceNumber: String)

    fun getRedeemCashback(refNum: String, code: String)

}