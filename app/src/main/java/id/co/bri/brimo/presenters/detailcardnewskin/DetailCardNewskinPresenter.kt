package id.co.bri.brimo.presenters.detailcardnewskin

import id.co.bri.brimo.contract.IPresenter.detailcardnewskin.IDetailCardNewSkinPresenter
import id.co.bri.brimo.contract.IView.IDetailCardView
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserver
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.InboxRequest
import id.co.bri.brimo.models.apimodel.request.StatusRequest
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.InboxResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptAmkkmResponse.ReceiptAmkkmInboxResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptInternationalInboxResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptKaiInboxTravel
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.esbn.earlyredeem.CatatanAktivitasEarly
import id.co.bri.brimo.models.apimodel.response.nfcpayment.DetailRekeningResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.disposables.CompositeDisposable
import java.util.concurrent.TimeUnit

class DetailCardNewskinPresenter<V>(
    schedulerProvider: SchedulerProvider,
    compositeDisposable: CompositeDisposable,
    mBRImoPrefRepository: BRImoPrefSource,
    apiSource: ApiSource,
    transaksiPfmSource: TransaksiPfmSource
) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
    IDetailCardNewSkinPresenter<V> where V : IMvpView, V : IDetailCardView {
        var urlActivityList: String? = null
        var urlActivityDetailList: String? = null
        private var statusRequest: StatusRequest? = null
        private var confirmationRequest: InboxRequest? = null
        private var inboxResponse: InboxResponse? = null


    override fun getSaldoNormal(account: String) {
        getView().showSkeleton()
        val seqNum = brImoPrefRepository.seqNumber

        compositeDisposable.add(
            apiSource.getSaldoNormal(account, seqNum)
                .timeout(AppConfig.TIMEOUT_CONNECT.toLong(), TimeUnit.SECONDS)
                .subscribeOn(schedulerProvider.io())
                .observeOn(schedulerProvider.mainThread())
                .subscribeWith(object : ApiObserver(view, seqNum) {
                    override fun onFailureHttp(errorMessage: String) {
//                        getView().hideSkeleton()
                        getView().onException(errorMessage)
                    }

                    override fun onApiCallSuccess(response: RestResponse) {
                        getView().hideSkeleton()
                        val data = response.getData(DetailRekeningResponse::class.java)
                        getView()?.onSuccessGetSaldoNormal(data)
                    }

                    override fun onApiCallError(restResponse: RestResponse) {
                        getView().hideSkeleton()
                        if (restResponse.code.equals(Constant.RE_SESSION_END, ignoreCase = true)) {
                            getView().onSessionEnd(restResponse.desc)
                        } else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_12.value)) {
                            getView().onExceptionRevamp(restResponse.desc)
                        } else {
                            getView().onException(restResponse.desc)
                        }
                    }
                })
        )
    }

    override fun setUrlActivity(url: String) {
        this.urlActivityList = url
    }

    override fun setUrlActivityDetail(url: String) {
        this.urlActivityDetailList = url
    }

    override fun setTrxType(trxType: String?) {
        //DOnothing
    }

    fun setupData(
        periode: String?,
        status: String?,
        fitur: String?,
        subFitur: String?,
        lastId: String?
    ) {
        var periode = periode
        var status = status
        var fitur = fitur
        var subFitur = subFitur
        try {
            if (periode.equals("", ignoreCase = true) and status.equals("", ignoreCase = true) and
                fitur.equals("", ignoreCase = true)
            ) {
                periode = "ALL"
                status = "ALL"
                fitur = "ALL"
            }

            if (subFitur.equals("", ignoreCase = true)) {
                subFitur = "ALL"
            }

            if (lastId.equals("", ignoreCase = true)) {
                confirmationRequest = InboxRequest(fitur, subFitur, status, periode, "0")
            } else {
                confirmationRequest = InboxRequest(fitur, subFitur, status, periode, lastId)
            }
        } catch (e: java.lang.Exception) {
            // do nothing
        }
    }
}