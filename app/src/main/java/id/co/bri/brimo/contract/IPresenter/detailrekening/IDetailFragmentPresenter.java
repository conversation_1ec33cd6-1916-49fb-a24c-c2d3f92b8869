package id.co.bri.brimo.contract.IPresenter.detailrekening;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;

public interface IDetailFragmentPresenter<V extends IMvpView>
        extends IMvpPresenter<V> {

    void setChangeDefault(String accNumb);

    void getStatusKartu(String accNumb);

    void setUrl(String url);

    void setUrlStatus(String urlStatus);
}
