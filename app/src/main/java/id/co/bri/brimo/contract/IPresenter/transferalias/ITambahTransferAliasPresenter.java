package id.co.bri.brimo.contract.IPresenter.transferalias;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryBiFastRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryRtgsRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasBriRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRequest;

public interface ITambahTransferAliasPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {
    void getDataInquiry(InquiryTransferAliasRequest inquiryTransferAliasRequest);

    void getDataInquiryBri(InquiryTransferAliasBriRequest inquiryTransferAliasBriRequest);

    void getDataInquiryBiFast(InquiryBiFastRequest inquiryBiFastRequest);

    void getDataInquiryRtgs(InquiryRtgsRequest inquiryRtgsRequest);

    void setInquiryUrl(String url);

    void setInquiryUrlBri(String url);

    void setUrlBiFast(String url);

    void setInquiryUrlRtgs(String url);

}
