package id.co.bri.brimo.contract.IPresenter.smarttransfer

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.smarttransfer.ConfirmAccountBindingSmartTransferRequest

interface IDashboardSmartTransferPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUserConsentUrl(url: String)

    fun getUserConsent()

    fun setUpdateAccBindUrl(url: String)

    fun updateAccBind(briAccount: String, nonBriAccount: String, action: Boolean, status: Boolean, dest: String?, userConsent: Boolean, kodeBankLain: String?)

    fun setBindingSmartTransferUrl(url: String)

    fun confirmAccountBindingSmartTransfer(confirmBind: ConfirmAccountBindingSmartTransferRequest)

    fun setDeleteAccBindUrl(url: String)

    fun deleteAccBind(nonBriAccount: String?, briAccount: String?, kodeBankLain: String?)

    fun setUpdateUserConsentUrl(url: String)

    fun manageUserConsent(isDisable: Boolean)

    fun setAccListConsentUrl(url: String)

    fun getAccountListConsent()
}
