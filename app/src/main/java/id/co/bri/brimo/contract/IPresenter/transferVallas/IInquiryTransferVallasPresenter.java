package id.co.bri.brimo.contract.IPresenter.transferVallas;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryVallasRequest;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiKonversiVallasRequest;

public interface IInquiryTransferVallasPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {

    void getComfirmation(KonfirmasiKonversiVallasRequest request);

    void setConfirmationUrl(String inquiryUrl);

}
