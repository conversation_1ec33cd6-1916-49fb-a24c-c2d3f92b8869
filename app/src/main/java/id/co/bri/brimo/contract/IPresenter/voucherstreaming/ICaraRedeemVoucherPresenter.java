package id.co.bri.brimo.contract.IPresenter.voucherstreaming;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.DetailVoucherStreamingRequest;

public interface ICaraRedeemVoucherPresenter<V extends IMvpView> extends IMvpPresenter<V> {

    void setCaraRedeemUrl(String caraRedeemUrl);

    void getCaraRedeem(DetailVoucherStreamingRequest detailVoucherStreamingRequest);
}
