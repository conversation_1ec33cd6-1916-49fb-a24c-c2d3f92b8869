package id.co.bri.brimo.contract.IPresenter.britamarencana;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiDataRencanaRequest;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiRencanaRequest;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiUbahRencanaRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiRencanaResponse;

public interface IKonfirmasiDataRencanaPresenter  <V extends IMvpView>
        extends IMvpPresenter<V> {
    void setUrl(String url);

    void getKonfirmasiDataRencana(KonfirmasiDataRencanaRequest konfirmasiDataRencanaRequest);

    void getKonfirmasiUbahRencana(KonfirmasiUbahRencanaRequest konfirmasiUbahRencanaRequest);
}
