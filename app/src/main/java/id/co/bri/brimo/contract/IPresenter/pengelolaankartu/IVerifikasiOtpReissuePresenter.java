package id.co.bri.brimo.contract.IPresenter.pengelolaankartu;

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ResendOtpReissueReq;
import id.co.bri.brimo.models.apimodel.response.CodeValue;

public interface IVerifikasiOtpReissuePresenter<V extends IMvpView> extends IMvpPresenter<V> {
    void setUrlValidate(String url);

    void setUrlResend(String url);

    void resendOtp(ResendOtpReissueReq resendOtpReissueReq);

    void onValidateOtp(CodeValue CodeValue);
}