package id.co.bri.brimo.contract.IPresenter.dplkrevamp

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryDplkRegisRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ProductFtuDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.SimulasiDplkRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailPerformanceTabDplkResponse

interface IListDplkOptionRevampPresenter<V : IMvpView?> : IMvpPresenter<V> {

    fun setUrlSimulasiDplk(urlSimulasiDplk: String)

    fun setUrlInquiryDplk(urlInquryDplk: String)

    fun setUrlDetailPerformanceTabDplk(urlDetailPerformanceTab: String)

    fun setUrlProductFtuDplk(urlProductFtuDplk: String)


    fun getDataInquiryDplk(request: InquiryDplkRegisRequest)

    fun getDetailProductDpllk(
        request: ProductFtuDplkRequest,
        requestSimulasi: SimulasiDplkRequest,
        isCombination: Boolean,
    )

    fun getDataProductFtuDplk(request: ProductFtuDplkRequest, isCombination: Boolean)

    fun getDataSimulasiDplk(request: SimulasiDplkRequest)

    fun getDetailPerformanceTabDplk(request: ProductFtuDplkRequest)

    fun saveDataGraphicDplk(response: DetailPerformanceTabDplkResponse)


}