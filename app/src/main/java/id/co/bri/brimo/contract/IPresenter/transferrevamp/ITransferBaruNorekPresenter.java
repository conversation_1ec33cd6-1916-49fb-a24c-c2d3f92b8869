package id.co.bri.brimo.contract.IPresenter.transferrevamp;


import id.co.bri.brimo.contract.IPresenter.IMvpPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRevampRequest;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.SavedDataInquiryRequest;

public interface ITransferBaruNorekPresenter <V extends IMvpView>
        extends IMvpPresenter<V> {
    void getDataInquiry(InquiryTransferAliasRevampRequest inquiryTransferAliasRevampRequest);

    void getDataSavedListInquiry(SavedDataInquiryRequest savedDataInquiryRequest);

    void setInquiryUrl(String url);
}

