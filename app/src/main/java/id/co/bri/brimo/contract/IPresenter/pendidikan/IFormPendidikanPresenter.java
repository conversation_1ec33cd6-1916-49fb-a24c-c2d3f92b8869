package id.co.bri.brimo.contract.IPresenter.pendidikan;

import id.co.bri.brimo.contract.IPresenter.base.IBaseFormPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.models.apimodel.request.InquiryPdamRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryPendidikanRequest;

public interface IFormPendidikanPresenter <V extends IBaseFormView>
        extends IBaseFormPresenter<V> {
    void getDataInquiry(InquiryPendidikanRequest inquiryPendidikanRequest);

    void setUrlPending(String urlPending);
}
